import akshare as ak
import pandas as pd
from datetime import datetime, timedelta


def download_stock_minute_data(symbol, period, start_date, end_date, adjust="qfq"):
    """
    下载指定股票在日期范围内的分钟级别K线数据（带复权选项）
    
    参数:
    symbol (str): 股票代码，如 '000692'（不带市场前缀）
    period (str): K线周期，如 '5' 表示5分钟
    start_date (str): 开始日期，格式为 'YYYY-MM-DD'
    end_date (str): 结束日期，格式为 'YYYY-MM-DD'
    adjust (str): 复权类型，'qfq'表示前复权，'hfq'表示后复权，''表示不复权
    
    返回:
    pandas.DataFrame: 包含分钟级别K线数据的DataFrame
    """
    # 使用 stock_zh_a_hist_min_em 函数获取分钟级别数据（支持复权）
    try:
        stock_minute_df = ak.stock_zh_a_hist_min_em(
            symbol=symbol,
            period=period,
            start_date=start_date,
            end_date=end_date,
            adjust=adjust
        )
        
        # 检查数据是否为空
        if stock_minute_df.empty:
            print(f"警告：未获取到股票 {symbol} 在指定日期范围的数据")
            return pd.DataFrame()
            
        # 将日期时间列转换为datetime类型
        stock_minute_df['日期'] = pd.to_datetime(stock_minute_df['日期'])
        
        # 重命名列以便于使用
        stock_minute_df = stock_minute_df.rename(columns={
            '日期': 'datetime',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount'
        })
        
        return stock_minute_df
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return pd.DataFrame()


if __name__ == "__main__":
    # 设置参数
    stock_symbol = "sz000692"  # 惠天热电
    k_line_period = "5"  # 5分钟K线
    start_date = "2024-12-25"
    end_date = "2024-12-28"
    
    # 下载前复权数据
    result_df = download_stock_minute_data(
        symbol=stock_symbol,
        period=k_line_period,
        start_date=start_date,
        end_date=end_date,
        adjust="qfq"  # 前复权
    )
    
    # 显示数据基本信息
    print(f"获取到 {len(result_df)} 条记录")
    
    if not result_df.empty:
        print("\n数据预览:")
        print(result_df.head())
        
        # 按日期分组统计每天的数据量
        result_df['date'] = result_df['datetime'].dt.date
        daily_counts = result_df.groupby('date').size()
        print("\n每日数据量统计:")
        for date, count in daily_counts.items():
            print(f"{date}: {count}条记录")
        
        # 保存到CSV文件
        output_filename = f"{stock_symbol}_{k_line_period}min_qfq_{start_date}_to_{end_date}.csv"
        result_df.to_csv(output_filename, index=False)
        print(f"\n数据已保存到文件: {output_filename}")
    else:
        print("未获取到数据，请检查股票代码和日期范围是否正确")
