#!/usr/bin/env python
#  -*- coding: utf-8 -*-
import csv
print('n')
print('MT4专用，版权所有: ','n')
print(r'文件绝对路径示例:C:new_tdxqhT0002exportMA.TXT')
while True :
    print('n')
    a=input('请在此输入文件绝对路径并回车：')
    if not a : break
    try :
        data = open(a)
        hM1 = []
        for i in data:
            d = [x for x in i.strip().split(',')]
            hM1.append(d)
        hM1.pop()
        for i in hM1 :
            i[0] = i[0].replace('-','.')
            i[1] = i[1][:-2]+':'+i[1][-2:]+':00'
        hM1 = [i[:7] for i in hM1]    
        b=a[:-4]
        M1 = open(b+' 1分钟.csv',"w+",newline='',encoding='utf-8')
        writer = csv.writer(M1)
        writer.writerows(hM1)
        #print(hM1[-5:])
        data.close()
        M1.close()
        print('n')
        print('转换完成')
    except FileNotFoundError :
        print('文件路径不存在，请检查输入是否正确')    
    except PermissionError :
        print('文件'+b+' 1分钟.csv'+'被占用，请先关闭再写入')
    except OSError :
        print('路径前不得有空格，请检查输入是否正确')