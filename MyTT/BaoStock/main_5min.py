import baostock as bs
import pandas as pd
import os

# 确保目录存在
os.makedirs(os.path.dirname(os.path.abspath(__file__)), exist_ok=True)

# 登录系统
lg = bs.login()
# 显示登录返回信息
print('login respond error_code:' + lg.error_code)
print('login respond error_msg:' + lg.error_msg)

# 获取股票代码
stock_code = "sh.600895"  # 张江高科
start_date = '2024-12-01'
end_date = '2024-12-31'
frequency = "5"  # 5分钟K线

# 获取5分钟K线数据
# 分钟线指标：date,time,code,open,high,low,close,volume,amount,adjustflag
rs = bs.query_history_k_data_plus(stock_code,
    "date,time,code,open,high,low,close,volume,amount,adjustflag",
    start_date=start_date, 
    end_date=end_date,
    frequency=frequency, 
    adjustflag="3")  # 3表示前复权

print('query_history_k_data_plus respond error_code:' + rs.error_code)
print('query_history_k_data_plus respond error_msg:' + rs.error_msg)

# 打印结果集
data_list = []
while (rs.error_code == '0') & rs.next():
    # 获取一条记录，将记录合并在一起
    data_list.append(rs.get_row_data())
result = pd.DataFrame(data_list, columns=rs.fields)

# 转换 time 列为新的 data 列，并删除不需要的列
def convert_time_format(t):
    # 提取前8位日期和接下来的4位时间（HHMM）
    y = t[:4]
    m = t[4:6]
    d = t[6:8]
    hour = str(int(t[8:10]))  # 去除前导0，例如"09"变成"9"
    minute = t[10:12]
    return f"{y}.{m}.{d}.{hour}:{minute}"

# 利用 time 列生成新的 data 列
result["data"] = result["time"].apply(convert_time_format)

# 保留需要的列 (删除原始 date, time，以及amount, adjustflag字段)
result = result[["data", "code", "open", "high", "low", "close", "volume"]]

# 将 data 列重命名为 Time
result.rename(columns={"data": "Time"}, inplace=True)

# 在 code 和 open 列之间插入 name 列，值都为 '张江高科'
result.insert(2, "name", "张江高科")

# 结果集输出到csv文件
output_file = "sh.600895_5min_data.csv"
result.to_csv(output_file, index=False)
print(f"数据已保存到 {output_file}")

# 登出系统
bs.logout()
