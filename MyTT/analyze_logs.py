import os
import re


def analyze_log_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gb18030') as file:
                content = file.read()
        except UnicodeDecodeError:
            print(f"跳过文件 {file_path}: 无法解码")
            return 0, 0, 0

    # 提取总资产
    total_asset_match = re.search(r'总资产: (\d+\.\d+)', content)
    total_asset = float(total_asset_match.group(1)) if total_asset_match else 0

    # 统计买入次数
    buy_count = content.count('买入 -')

    # 统计卖出次数
    sell_count = content.count('卖出 -')

    return total_asset, buy_count, sell_count


def categorize_profit(total_asset, initial_capital=100000):
    profit_ratio = (total_asset - initial_capital) / initial_capital
    if abs(profit_ratio) <= 0.05:
        return '微赚' if profit_ratio >= 0 else '微亏'
    elif 0.05 < abs(profit_ratio) <= 0.15:
        return '小赚' if profit_ratio > 0 else '小亏'
    elif 0.15 < abs(profit_ratio) <= 0.30:
        return '中赚' if profit_ratio > 0 else '中亏'
    elif 0.30 < abs(profit_ratio) <= 0.50:
        return '大赚' if profit_ratio > 0 else '大亏'
    else:
        return '暴赚' if profit_ratio > 0 else '暴亏'


def analyze_logs(log_folder):
    log_files = [os.path.join(log_folder, f) for f in os.listdir(log_folder) if f.endswith('.txt')]

    crashed_stocks = []  # 用于存储暴跌个股的代码
    valid_stocks = 0
    total_assets = []
    buy_counts = []
    sell_counts = []
    profit_categories = {
        '微赚': 0, '微亏': 0,
        '小赚': 0, '小亏': 0,
        '中赚': 0, '中亏': 0,
        '大赚': 0, '大亏': 0,
        '暴赚': 0, '暴亏': 0
    }

    for log_file in log_files:
        total_asset, buy_count, sell_count = analyze_log_file(log_file)
        if buy_count > 0:
            valid_stocks += 1
            stock_code = os.path.splitext(os.path.basename(log_file))[0].split('_')[0]
            total_assets.append(total_asset)
            buy_counts.append(buy_count)
            sell_counts.append(sell_count)

            category = categorize_profit(total_asset)
            profit_categories[category] += 1
            if category == '暴亏':
                crashed_stocks.append(stock_code)

    if valid_stocks == 0:
        print("没有任何股票有买卖记录,无法进行统计分析")
        return

    avg_total_asset = sum(total_assets) / len(total_assets)
    avg_buy_count = sum(buy_counts) / len(buy_counts)
    avg_sell_count = sum(sell_counts) / len(sell_counts)

    print(f"平均总资产: {avg_total_asset:.2f}")
    print(f"平均买入次数: {avg_buy_count:.2f}")
    print(f"平均卖出次数: {avg_sell_count:.2f}")

    print("\n盈利统计:")
    for category in ['微赚', '小赚', '中赚', '大赚', '暴赚']:
        count = profit_categories[category]
        percentage = count / valid_stocks * 100
        print(f"{category}: {count} 只股票, 占比 {percentage:.2f}%")

    print("\n亏损统计:")
    for category in ['微亏', '小亏', '中亏', '大亏', '暴亏']:
        count = profit_categories[category]
        percentage = count / valid_stocks * 100
        print(f"{category}: {count} 只股票, 占比 {percentage:.2f}%")

    print("\n暴亏个股代码:")
    print(', '.join(crashed_stocks))

    print(f"\n总共 {len(log_files)} 只股票, {valid_stocks} 只有买卖记录并参与统计, "
          f"{len(log_files) - valid_stocks} 只没有买卖记录不参与统计")


if __name__ == "__main__":
    log_folder = "logs"
    analyze_logs(log_folder)
