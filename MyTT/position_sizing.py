#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
利用最大回撤、ATR波动率和基于凯利公式（仓位计算公式）的方式动态计算某只股票的仓位占比，

封装为一个 PositionSizer 类，方便后续使用。

基本流程：
1. 利用 talib 计算 ATR 波动率（True Range 的平均值），默认周期为 14 天；
2. 计算最大回撤比例：采用过去 180 天（可调）的动态滑动窗口计算；
3. 仓位计算公式：
       position = (win_prob / volatility) * (1 - max_drawdown)
   其中 win_prob 为获胜概率，volatility 为 ATR 得到的当前波动率；
4. 最终建议仓位占比为：
       final_fraction = scaling_factor * min(max(pos_size, 0), risk_limit)
   其中 risk_limit = 1 - max_drawdown，scaling_factor 为安全系数，例如 0.5。

注意：实际应用中需要根据具体策略调整获胜概率、ATR 周期、滑动窗口天数以及安全系数。

参考链接：[MetaSo参考示例](https://metaso.cn/search/8580223082694414336?q=%E8%AF%84%E4%BC%B0%E6%8A%95%E8%B5%84%E7%BB%84%E5%90%88%E6%B3%A2%E5%8A%A8%E6%80%A7%E3%80%81%E6%9C%80%E5%A4%A7%E5%9B%9E%E6%92%A4%E7%AD%89%E6%8C%87%E6%A0%87%EF%BC%8C%E7%BB%93%E5%90%88%E5%87%AF%E5%88%A9%E5%85%AC%E5%BC%8F%E5%8A%A8%E6%80%A7%E8%B0%83%E6%95%B4%E5%8D%A1%E4%BA%A4%E6%98%93%E7%9A%84%E8%B5%84%E9%87%91%E5%8D%A0%E6%AF%94%EF%BC%8C%E4%BB%A5%E5%8F%8A%E9%80%9A%E8%BF%87ATR%E5%86%B3%E5%AE%9A%E6%9F%90%E5%8F%AA%E8%82%A1%E7%A5%A8%E5%BC%80%E4%BB%93%E6%95%B0%E9%A2%9D%E7%9A%84%E7%90%86%E8%AE%BA%E5%92%8C%E5%BC%80%E6%BA%90%E9%A1%B9%E7%9B%AE)

作者：Your Name
日期：2023-xx-xx
"""

import numpy as np
import talib


class PositionSizer:
    def __init__(self, atr_period=14, sliding_window=180, scaling_factor=0.5, default_win_prob=None):
        """
        初始化 PositionSizer
        
        参数：
            atr_period: ATR 计算周期，默认 14 天
            sliding_window: 计算最大回撤的滑动窗口大小，默认 180 天
            default_win_prob: 默认获胜概率（例如 0.55 表示 55%），如果不指定，调用时需要提供
        """
        self.atr_period = atr_period
        self.sliding_window = sliding_window
        self.default_win_prob = default_win_prob

    def compute_relative_ATR(self, df):
        """
        计算 ATR 指标（True Range 的平均值）
        
        参数:
            df: DataFrame, 必须包含 'high', 'low', 'close' 列
        返回:
            每个周期的 ATR 值（ndarray）
        """
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        atr = talib.ATR(high, low, close, timeperiod=self.atr_period)
        # 计算相对波动率：ATR 除以当前价格（这里采用数据窗口最后一天的收盘价）
        relative_atr = atr / close[-1]
        return relative_atr

    def compute_max_drawdown(self, df):
        """
        根据传入 DataFrame 计算最大回撤，打印中间计算过程
        
        参数:
            df: DataFrame，必须包含 'close' 列，且数据按时间升序排列
        返回:
            最大回撤比例（0~1之间）
        """
        max_drawdown = 0
        window = self.sliding_window
        print("=== 最大回撤计算过程 ===")
        if len(df) < window:
            window = len(df)
            print(f"数据长度不足 {self.sliding_window} 行，使用全部 {window} 行数据")
        for i in range(window, len(df) + 1):
            window_df = df.iloc[i - window:i]
            close_vals = window_df['close'].values
            cumulative_max = np.maximum.accumulate(close_vals)
            drawdowns = (cumulative_max - close_vals) / cumulative_max
            local_max_dd = np.max(drawdowns)
            print(f"窗口 [{i-window}:{i}]，Close = {close_vals}, 累计最高 = {cumulative_max}, 回撤 = {drawdowns}, 局部最大回撤 = {local_max_dd}")
            if local_max_dd > max_drawdown:
                max_drawdown = local_max_dd
        print("最终最大回撤：", max_drawdown)
        return max_drawdown

    def calculate_position_size(self, win_prob, volatility):
        """
        计算仓位比例的公式：
        """
        if volatility == 0:
            return 0
        
        raw_val = win_prob / volatility
        # 限制仓位比例的返回值在最低10和最高50之间
        bounded_val = min(max(raw_val, 10), 50)
        return bounded_val
