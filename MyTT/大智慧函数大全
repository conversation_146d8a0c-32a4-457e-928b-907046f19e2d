1.行情函数
ADVANCE
含义:取得该周期上涨家数;
阐释:本函数仅对大盘有效

ALLASKVOL
含义:取得所有委托卖出单的总量。
单位:手
阐释: 该函数仅对Level2 行情显示有效。

ALLBIDVOL

含义:取得所有委托买入单的总量。
单位:手
阐释: 该函数仅对Level2 行情显示有效

ALLORDERVOL
含义:分时线中该分钟新增的按照委托单大小划分的委托量，委托单分为买入委托和卖出委托，该函数仅对深圳Level2行情显示有效。
单位:手
阐释:ALLORDERVOL(N,M),N表示类型，N=1买入委托单，N=2卖出委托单，M表示委托单大小，M=0小单，M=1中单，M=2大单，M=3特大单。例如：ALLORDERVOL(1,3)/(ALLORDERVOL(1,0) ALLORDERVOL(1,1) ALLORDERVOL(1,2) ALLORDERVOL(1,3))*100表示特大单委托量占总委托量的百分比

AMOUNT 

含义:取得该周期成交额。
单位:元

ASKPRICE(N)

含义:取得委卖1—委卖3 价格。
参数: N 取值范围1—3，分别表示委卖1、委卖2、委卖3 的价格
单位:元
阐释:例如ASKPRICE(1)则表示取委卖1 的价格，本函数仅个股在分笔成交分析周期有效。

ASKVOL(N)
含义:取得委卖1--委卖3 量。
参数: N 取值范围1—3，分别表示委卖1、委卖2、委卖3 的成交量
单位:手
阐释:例如ASKVOL (1)则表示取委卖1 的成交量，本函数仅个股在分笔成交分析周期有效。

AVGASKPRICE
含义:取得所有委托卖出单的加权平均委托价格
单位:元
阐释: 该函数仅对Level2 行情显示有效。

AVGBIDPRICE

含义: 取得所有委托买入单的加权平均委托价格
单位:元
阐释: 该函数仅对Level2 行情显示有效。

AVGORDERSIZE(N) 

含义:取得平均每一单委托单的大小。该函数仅对深圳Level2行情显示在分时线中有效。
单位:元
阐释: AVGORDERSIZE(N),N表示类型，N=1买入委托单，N=2卖出委托单。
例如:AVGORDERSIZE(1)表示所有买入委托单的平均单量

BARSTATUS
含义:返回数据位置信息
阐释:BARSTATUS 返回数据位置信息，1 表示第一根K 线，2 表示最后一个数据，0 表示中间位置。例如：BARSTATUS=2 表示当天是该股票数据的最后一个周期。

BIDPRICE(N)
含义:取得委买1—委买3 价格。
参数: N 取值范围1—3，分别表示委买1、委买2、委买3 的价格
单位:元
阐释:例如BIDPRICE (1)则表示取委买1 的价格,本函数仅个股在分笔成交分析周期有效。

BIDVOL(N)
含义:取得委买1--委买3 量。
参数:N 取值范围1—3，分别表示委买1、委买2、委买3 的成交量
单位:手
阐释:例如BIDVOL (1)则表示取委买1 的成交量,本函数仅个股在分笔成交分析周期有效。

BIGORDER(N,M)
含义:日线取得该日成交的中单、大单、特大占总的成交量比例,委托
单分为买入委托和卖出委托
参数: N 表示类型，N=1 买入委托单，N=2 卖出委托单，M 表示委托单大小，M=1 中单,M=2 大单，M=3 特大单，M 可以省略，表示大单。注意中单包含了大单，而大单则包含了特大单，所以小单比例可以表示为1-BIGORDER(1,1)。
阐释:例如BIGORDER(1,3)表示买入特大单占总成交量的比例。该函数仅对Level2 行情显示有效。

BUYVOL 

含义:取得主动性买单量。
单位:手
阐释: 本函数仅个股在分笔成交分析周期有效，当本笔成交为主动性买盘时,其数值等于成交量,否则为0。

CLOSE 

含义:取得该周期收盘价。
单位:元

DECLINE 

含义:取得该周期下跌家数。
阐释:本函数仅对大盘有效

DISPSTATUS 

含义: 返回数据显示信息
阐释:DISPSTATUS 返回数据显示信息，1 表示显示区域的第一根K 线，2 表示显示区域最后一根K 线， 0 表示其它位置。例如：DISPSTATUS=1 表示当天是图形显示中的第一个周期。

DIVIDENDBARS(N)
含义:派息到现在的周期数。
参数:N 表示第N 次派息
阐释:DIVIDENDBARS(N),取得之前第N 次派息到当前的周期数，例
如：DIVIDENDBARS(0)=0 表示当天发生派息。

DIVIDEND(N)
含义:每股派息数量。
参数: N 表示第N 次派息
单位:元
阐释:DIVIDEND(N), 取得之前第N 次每股派息数量， 例如：
DIVIDEND(0)表示最近一次派息的数量。

EXTDATA 

含义:取得日线扩展数据(1—19)。
参数: N 取1—19
阐释:例如EXTDATA(3)就表示扩展数据3。本函数仅在日线分析周期有效。

EXTRADATA(S)
含义:取得附加数据。
阐释:EXTRADATA(S),取得名为S 的附加数据

HIGH
含义:取得该周期最高价。
单位:元

INDEXA 

含义:表示同期大盘的成交额
单位:元
阐释:该函数对分笔成交分析周期无效

INDEXADV
含义:表示同期大盘的上涨家数
阐释:该函数对分笔成交分析周期无效

INDEXC
含义:表示同期大盘的收盘价
阐释:该函数对分笔成交分析周期无效

INDEXDEC

含义:表示同期大盘的下跌家数
阐释:该函数对分笔成交分析周期无效

INDEXH
含义:表示同期大盘的最高价
阐释:该函数对分笔成交分析周期无效

INDEXL
含义:表示同期大盘的最低价
阐释:该函数对分笔成交分析周期无效

INDEXO
含义:表示同期大盘的开盘价
阐释:该函数对分笔成交分析周期无效

INDEXV
含义:表示同期大盘的成交量
单位:手
阐释:该函数对分笔成交分析周期无效

ISBUYORDER
含义:取得该成交是否为主动性买单。
阐释: 本函数仅个股在分笔成交分析周期有效，当本笔成交为主动性
买盘时,返回1,否则为0

ISDOWN

含义:该周期是否收阴。
阐释:当收盘<开盘时，返回值为1，否则为0< p="">

ISEQUAL

含义:该周期是否平盘。
阐释:当收盘=开盘时，返回值为1，否则为0

ISUP

 含义:该周期是否收阳。
阐释:当收盘>开盘时，返回值为1，否则为0

LOW
含义:该周期最低价

NEWORDER(N)
含义:取得当前周期中新增的委托单的委托量(估计值),
单位;手
阐释:NEWORDER(N),取得当前周期新增的委托量，N=1 表示委托买入新单，N=2 表示委托卖出新单,该函数仅对Level2 行情显示有效。

NOTICE(N)
含义:分时线取得分钟短线精灵的数量。
阐释:NOTICE(N),N短线精灵类型，1:火箭发射,2:快速反弹,3:高台跳水,4:加速下跌,5:大笔买入,6:大笔卖出,7:封涨停板,8:封跌停板,9:打开涨停,10:打开跌停,11:有大卖盘,12:有大买盘,13:拉升指数,14:打压指数,15:机构买单,16:机构卖单,17:机构吃货,18:机构吐货,19:分单买单,20:分单卖单,21:买入撤单,22:卖出撤单,23:买入新单,24:卖出新单。例如NOTICE(15)表示机构买单数量。

OPEN
含义:该周期开盘价

OPENINTEREST
含义:取得该周期持仓量。
阐释:OPENINTEREST,取得该周期持仓量。该函数仅对期货有效。

 ORDER(N)
含义:日线取得该日成交的委托单数量,委托单分为买入委托和卖出委托,
参数: N 表示类型，N=1 买入委托单，N=2 卖出委托单
阐释:该函数仅对Level2 行情显示有效。

ORDERNUM(N,M)
含义:分时线中该分钟成交的委托单数量,委托单分为买入委托和卖出委托,
参数: N 表示类型，N=1 买入委托单，N=2 卖出委托单，M 表示委托单大小，M=0 所有委托单，M=1 中单,M=2 大单，M=3 特大单。

ORDERNUM(N,M)
阐释:例如ORDERNUM(1,3)表示特大买单数。该函数仅对Level2 行情
显示有效。

ORDERVOL(N,M)
含义:分时线中该分钟成交的按照委托单大小划分的成交量,委托单分为买入委托和卖出委托。
参数:N 表示类型，N=1 买入委托单，N=2 卖出委托单，M 表示委托单大小，M=0 所有委托单，M=1 中单,M=2 大单，M=3 特大单。
单位:手
阐释:例如ORDERVOL(1,2)/ORDERVOL(1,0)*100 表示大单成交量占总成交量的百分比。该函数仅对Level2 行情显示有效。

SELFDATA(S)
含义:取得名为S 的自定义数据
参数:S 为自定义数据名称
阐释:本函数仅在日线分析周期有效

SELLVOL 

含义: 主动性卖单成交量
单位:手
阐释: 本函数仅个股在分笔成交分析周期有效，当本笔成交为主动性
卖盘时,其数值等于成交量,否则为0

SPLIT(N)
含义: SPLIT(N)取得之前第N 次除权(送股或配股)的除权比例，表示除权后股价将下跌该比例
参数:N 表示第N 次除权
阐释:例如：SPLIT(0)=0.5 表示最近一次除权可能是10 送10，股价下跌一半。

SPLITBARS
含义:SPLITBARS(N)取得之前第N 次除权到当前的周期数
参数:N 表示第N 次除权
阐释:例如：SPLITBARS(0)=0 表示当天发生除权。

TICKCOUNT
含义:取得该周期成交的笔数。

 TRANSACT(N,M)
含义:分时线中该分钟成交的逐笔成交数量,成交分为单分主动买入和主动卖出成交。
参数:TRANSACT(N,M),N 表示类型，N=0 表示所有成交，N=1 主动买入，N=2 主动卖出，M 表示委托单大小，M=0 所有成交，M=1 中单,M=2大单，M=3 特大单。
阐释: 例如TRANSACT(1,3)表示主动买入的特大单数，该函数仅对Level2 行情显示有效。

TRANSACTVOL(N,M)
含义:分时线中该分钟成交的按照逐笔成交量大小划分的成交量,成交分为单分主动买入和主动卖出成交。
参数:TRANSACTVOL(N,M),N 表示类型，N=0 表示所有成交，N=1 主动买入，N=2 主动卖出，M 表示委托单大小，M=0 所有成交，M=1中单,M=2 大单，M=3 特大单。
单位:手
阐释:例如:TRANSACTVOL(1,2)/TRANSACTVOL(1,0)*100 表示逐笔大单成交量占总成交量的百分比，该函数仅对Level2 行情显示有效。

VOL
含义:取得该周期成交量。
单位:手

WIDTHDRAWORDER(N)
含义:取得当前周期的撤单量(估计值)
参数:N=1 表示委托买入撤单，N=2 表示委托卖出撤单
单位:手
阐释: 该函数仅对Level2 行情显示有效。

ZBS 
含义:取得该周期总笔数。


2.时间函数
BARPOS
含义:取得该周期在所有数据中的位置。
阐释:对于日线来说，函数返回上市以来的天数。

D1970TODATE(X)
含义:1970 日转换为日期。
阐释:D1970TODATE(X)，得到1970 日期X 的日期值。

DATE
含义:取得该周期从1900 以来的年月日。
阐释: 函数返回有效值范围为(700101-1341231), 表示19700101-20341231。

DATETOD1970(X)
含义:得到日期X 距离1970 年1 月1 日以来的天数
阐释:例如,DATETOD1970(DATE)就返回今天距离1970 年1月1 日的天数。

DAY
含义:取得该周期的日期。
阐释:函数返回有效值范围为(1-31),比如今天是交易日，日期
为7 月18，day 返回的就是18。

DAYS1970
含义:取得该周期从1970 以来的天数。
阐释:DAYS1970,函数返回自从1970 年1 月1 日以来的天数，例如在1971 年1 月1 日返回365。

HOUR
含义:取得该周期的小时数。
阐释:函数返回有效值范围为(0-23)，对于日线及更长的分析周期值为0。

LDAY
含义:取得该周期农历日期
阐释:函数返回有效值范围为(1-30)，比如2007 年7 月18 日农历为6 月5 日，day 返回的就是5。

LMONTH
含义:取得该周期的农历月份。
阐释:函数返回有效值范围为(1-12)

LYEAR
含义: 取得该周期的农历年份
阐释:函数返回有效值范围为(1970-2038)。

MINUTE

含义:取得该周期的分钟数。
阐释:函数返回有效值范围为(0-59)，对于日线及更长的分析周期值为0。

MONTH
含义:取得该周期的月份。
阐释:函数返回有效值范围为(1-12)。

TOTOTIME(X)
含义: 秒数转换位时间
阐释:T0TOTIME(X)，得到从0 点开始X 秒后的时间值。

TIME
含义: 取得当前的时间，其有效值的表达式为HH/MM/SS
阐释：函数返回有效值范围为(000000-235959) ，例如当天的时间为12 点5 分30 秒，那么使

用TIME 取得数值为120530，注意采用24 小时制；

TIME0
含义:取得该周期从当日0 点以来的秒数。
阐释:函数返回自从当日0 点以来的秒数，对于日线以上的分析周期，返回0。

TIMETOTO(X)
含义: 得到时间X 距离当日0 点的秒数
阐释:例如TIMETOTO(120000)则返回值43200

WEEKDAY
含义:取得该周期的星期数
阐释:函数返回有效值范围为(0-6)，0 表示星期天

YEAR
含义: 取得该周期的年份
阐释:函数返回有效值范围为(1970-2038)



3.引用函数
ALL(X,N)
含义:是否一直满足条件。
阐释:ALL(X,N),统计N 周期中是否一直都满足X 条件,若N=0则从第一个有效值开始。例如：ALL(CLOSE>OPEN,20)表示是否20 周期内全部都收阳线。

ANY(X,N)
含义:ANY(X,N),统计N 周期中是否至少有一次满足X 条件,若N=0 则从第一个有效值开始。
阐释:例如：ANY(CLOSE>OPEN,20)表示是否20 周期内是否存在一根阳线。

BACKSET(X,N)
含义:将当前位置到若干周期前的数据设为1。
阐释:BACKSET(X,N),若X 非0,则将当前位置到N 周期前的数值设为1。例如：BACKSET(CLOSE>OPEN,2)若收阳则将该周期及前一周期数值设为1,否则为0。

BARSCOUNT(X)
含义:求总的周期数。
阐释:BARSCOUNT(X)第一个有效数据到当前的天数。例如：BARSCOUNT(CLOSE)对于日线数据取得上市以来总交易日数，对于分笔成交取得当日成交笔数，对于1 分钟线取得当日交易分钟数。

BARSLAST(X)
含义:上一次条件成立到当前的周期数。
阐释:BARSLAST(X):上一次X 不为0 到现在的天数。例如：BARSLAST(CLOSE/REF(CLOSE,1)>=1.1)表示上一个涨停板到当前的周期数。

BARSSINCE(X)
含义:第一个条件成立到当前的周期数。
阐释:BARSSINCE(X):第一次X 不为0 到现在的天数。例如：BARSSINCE(HIGH>10)表示股价超过10 元时到当前的周期数。

COUNT(X,N)
含义:统计满足条件的周期数。
阐释:COUNT(X,N),统计N 周期中满足X 条件的周期数,若N=0 则从第一个有效值开始。例如：COUNT(CLOSE>OPEN,20)表示统计20 周期内收阳的周期数。 

DMA(X,A)
含义:DMA(X,A),求X 的A 日动态移动平均。
算法: 若Y=DMA(X,A)，则Y=A*X+(1-A)*Y',其中Y'表示上一周期Y 值,A 必须小于1。
阐释:例如DMA(CLOSE,VOL/CAPITAL)表示求以换手率作平滑因子的平均价。

EMA(X,N)
含义:求指数平滑移动平均。EMA(X,N),求X 的N 日指数平滑移动平均。
算法：若Y=EMA(X,N)，则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y 值。

FILTER(X,N)
含义:过滤连续出现的信号。
阐释: FILTER(X,N):X 满足条件后，将其后N 周期内的数据置为0, 若N 为0 表示将其后的所有数据置0 。例如FILTER(CLOSE>OPEN,5)查找阳线，5 天内再次出现的阳线不被记录在内。

HHV(X,N)
含义:HHV(X,N),求N 周期内X 最高值,N=0 则从第一个有效值开始。
阐释:例如，HHV(HIGH,30)表示求30 日最高价。

HHVALL(X)
含义:HHVALL(X),求图形可视范围内X 最高值。
阐释:例如，HHVALL(HIGH)表示图形范围内的最高价,该函数用于循环或分支中将不准确。

HHVBARS(X,N)
含义:求上一高点到当前的周期数。
阐释:HHVBARS(X,N)，求N 周期内X 最高值到当前周期数，N=0 表示从第一个有效值开始统计。例如：HHVBARS(HIGH,0)求得历史新高到到当前的周期数。

LAST(X) 
含义:LAST(X),统计连续满足X 条件的周期数。
阐释:例如，LAST(CLOSE>OPEN)表示到目前为止连续收阳的周期数。

LLV(X,N)
含义:LLV(X,N),求N 周期内X 最低值,N=0 则从第一个有效值开始。
阐释:例如，LLV(LOW,0)表示求历史最低价。

LLVALL(X)
含义:LLVALL(X),求图形可视范围内X 最低值。
阐释:例如，LLVALL(LOW)表示图形范围内的最低价,该函数用于循环或分支中将不准确。

LLVBARS(X,N)
含义:求上一低点到当前的周期数。
阐释:LLVBARS(X,N):求N 周期内X 最低值到当前周期数，N=0 表示从第一个有效值开始统计， 例如：LLVBARS(HIGH,20)求得20 日最低点到当前的周期数。

MA(X,N)
含义:MA(X,N),求X 的N 日移动平均值。
算法：(X1 X2 X3 ... Xn)/N
阐释:例如，MA(CLOSE,10)表示求10 日均价,特例：MA(x,0)表示x 所有数据的平均。

MEMA(X,N)
含义:MEMA(X,N),求X 的N 日改良指数平滑移动平均。
算法：若Y=MEMA(X,N)，则Y=[X (N-1)*Y']/N,其中Y'表示上一周期Y 值。
阐释:例如：MEMA(CLOSE,30)表示求30 日改良指数平滑均价。

 REF(X,A)
含义:引用若干周期前的数据。
阐释:REF(X,A),引用A 周期前的X 值。例如：REF(CLOSE,1)表示上一周期的收盘价，在日线上就是昨收。

SMA(X,N,M)
含义:SMA(X,N,M),求X 的N 日移动平均，M 为权重。
算法: 若Y=SMA(X,N,M)，则Y=[M*X (N-M)*Y')/N,其中Y'表示上一周期Y 值,N 必须大于M。
阐释:例如，SMA(CLOSE,30,1)表示求30 日移动平均价。

SUM(X,N)
含义:SUM(X,N),统计N 周期中X 的总和,N=0 则从第一个有效值开始。
阐释:例如，SUM(VOL,0)表示统计从上市第一天以来的成交量总和。

SUMBARS(X,A)
含义:向前累加到指定值到现在的周期数。
阐释:SUMBARS(X,A):将X 向前累加直到大于等于A,返回这个区间的周期数。例如，SUMBARS(VOL,CAPITAL)求完全换手到现在的周期数。

WMA(X,N)
含义:WMA(X,N), 求X 的N 日加权移动平均。
算法:Y=WMA[X,N]=(1*X1 2*X2 3*X3 ... N*Xn)/(1 2 3 ... N)。
阐释:例如，WMA(CLOSE,20)表示求20 日加权移动平均价格。


4.逻辑函数
BETWEEN(A,B,C)
含义:介于两个数之间。
阐释:BETWEEN(A,B,C)表示A 处于B 和C 之间时返回1，否则返回0 。例如，BETWEEN(CLOSE,MA(CLOSE,10),MA(CLOSE,5)) 表示收盘价介于5 日均线和10 日均线之间。

CROSS(A,B)
含义:两条线交叉。
阐释:CROSS(A,B)表示当A 从下方向上穿过B 时返回1，否则返回0。例如：CROSS(MA(CLOSE,5),MA(CLOSE,10))表示5 日均线与10 日均线交金叉。

IF(X,A,B)
含义:根据条件取得不同的值。
阐释:IF(X,A,B)若X 不为0 则返回A,否则返回B。例如：IF(CLOSE>OPEN,HIGH,LOW)表示该周期收阳则返回最高值，否则返回最低值。

IFS(X,A,B)
含义:根据条件取得不同的字符串值。
阐释:IFS(X,A,B)若X 不为0 则返回A,否则返回B。例如：IF(CLOSE>OPEN,'上涨','下跌')表示该周期收阳则返回字符串'上涨',否则返回'下跌'。

LONGCROSS(A,B,N)
含义:两条线维持一定周期后交叉。
阐释:LONGCROSS(A,B,N)表示A 在N 周期内都小于B，本周期从下方向上穿过B 时返回1，否则返回0。例如：LONGCROSS(MA(CLOSE,5),MA(CLOSE,10),5)表示5日均线维持5 周期后与10 日均线交金叉。

RANGE(A,B,C)
含义:介于某个范围之间。
阐释:RANGE(A,B,C)表示A 大于B 同时小于C 时返回1，否则返回0。例如：RANGE(CLOSE,MA(CLOSE,5),MA(CLOSE,10))表示收盘价大于5 日均线并且小于10 日均线。


5.算数函数
ABS(X)
含义:ABS(X)，返回X 的绝对值。
阐释:例如，ABS(-34)返回34。

ACOS(X) 
含义:ACOS(X)返回X 的反余弦值

AND
含义: 逻辑与运算。
阐释:A AND B；表示条件A 与条件B 同时成立。

ASIN(X)
含义:ASIN(X)返回X 的反正弦值。

ATAN(X)
含义:ATAN(X)返回X 的反正切值。

CEILING(A)
含义:向数值增大方向舍入。
阐释:CEILING(A)返回沿A 数值增大方向最接近的整数。
例如：CEILING(12.3)求得13,CEILING(-3.5)求得-3。

COS(X)
含义:COS(X)返回X 的余弦值。

EXP(X)
含义:EXP(X)为e 的X 次幂。
阐释:例如，EXP(CLOSE)返回e 的CLOSE 次幂。

FLOOR(A)
含义:向数值减小方向舍入。
阐释:FLOOR(A)返回沿A 数值减小方向最接近的整数。
例如：FLOOR(12.3)求得12,FLOOR(-3.5)求得-4。

FRACPART(A)
含义:FRACPART(A)返回数值的小数部分。
阐释:例如FRACPART(12.3)求得0.3,FRACPART(-3.5)求得-0.5。

INTPART(A)
含义:绝对值减小取整，即取得数据的整数部分。
阐释:INTPART(A)返回沿A 绝对值减小方向最接近的整数。
例如：INTPART(12.3)求得12,INTPART(-3.5)求得-3。

LN(X)
含义:LN(X)以e 为底的对数。
阐释:例如，LN(CLOSE)求收盘价的对数。

LOG(X)
含义:LOG(X)取得X 的对数。
阐释:例如，LOG(100)等于2MAX(A,B...)。

MAX(A,B...)

含义:MAX(A,B...)返回所有参数的中的最大值，参数数量可以有2—16个
阐释:例如，MAX(CLOSE,OPEN,REF(CLOSE,1))表示返回昨收、今开、收盘三个价格中最高的价格。

MIN(A,B...)
含义:MIN(A,B...)返回所有参数的中的最小值，参数数量可以有2—16 个。
阐释:例如，MIN(CLOSE,OPEN,REF(CLOSE,1))表示返回昨收、今开、收盘三个价格中最低的价格。

MOD(A,B)
含义:MOD(A,B)返回A 对B 求模。
阐释:例如，MOD(26,10)返回6。

NOT
含义:NOT(X)返回非X,即当X=0 时返回1，否则返回0。
阐释:例如，NOT(ISUP)表示平盘或收阴。

OR
阐释:A OR B，表示条件A 与条件B 只要有一个成立即可。

POW
含义:POW(A,B)返回A 的B 次幂。
阐释:例如，POW(CLOSE,3)求得收盘价的3 次方。

REVERSE
含义:REVERSE(X)返回-X。
阐释:例如REVERSE(CLOSE)返回-CLOSE。

SGN
阐释:SGN(X)，当X>0,X=0,X<0 分别返回1,0,-1。

SIN
阐释:SIN(X)返回X 的正弦值。

SQRT
阐释:SQRT(X)为X 的平方根，例如：SQRT(CLOSE)收盘价的。

TAN
阐释:TAN(X)返回X 的正切值。


6.统计函数
AVEDEV(X，N)
含义： 平均绝对偏差，求X 的N 日平均绝对偏差。
参数：X：变量，计算样本   N：计算周期数。

DEVSQ(X，N)
含义： 数据偏差平方和，求X 的N 日数据偏差平方和。
参数：X：变量，计算样本   N：计算周期数。

FORCAST(X，N)
含义： 线性回归预测值，求X 的N 周期线性回归预测值。
参数：X：变量，计算样本   N：计算周期数。
例：FORCAST(CLOSE，10)：表示求10 周期线性回归预测本周期收盘价。

SLOPE(X，N)
含义： 线性回归斜率，求X 的N 周期线性回归线的斜率。
参数：X：变量，计算样本   N：计算周期数。
例：SLOPE(CLOSE，10)：求10 周期线性回归线的斜率。

STD(X，N)
含义： 估算标准差，求X 的N 周期估算标准差。
参数：X：变量，计算样本   N：计算周期数。
例STD(CLOSE，10)：求10 周期收盘价的估算标准差。

STDDEV(X,N)
含义:标准差, 求X 的N 周期标准差。
参数：N：计算周期数。

STDP(X，N)
含义： 总体标准差，求X 的N 日总体标准差。
参数：X：变量，计算样本   N：计算周期数。

VAR(X，N)
含义： 估算样本方差，求X 的N 日估算样本方差。
参数：X：变量，计算样本   N：计算周期数。

VARP(X，N)
含义： 总体样本方差，求X 的N 日总体样本方差。
参数：X：变量，计算样本   N：计算周期数


7.指标函数
COST
含义： COST(10),表示10%获利盘的价格是多少，即有10%的。
持仓量在该价格以下，其余90%在该价格以上，为套牢盘。该函数仅对日线分析周期有效。

COSTB
含义： COSTB(10),表示10%获利盘的价格是多少，即有10%。
的持仓量在该价格以下，其余90%在该价格以上，为套牢盘该函数仅对日线分析周期有效。

CYC
含义：CYC(N)成本均线指标是个量价均发挥作用的均线，分别代表n 日的市场平均建仓成本，因而也叫成本均线。

CYW
含义： 以收盘价位置为系数的换手率移动平均。

FLATZIG
含义：FLATZIG(K,N,ABS),当价格变化量超过N%时转向。
参数：K 表示0:开盘价,1:最高价,2:最低价,3:收盘价,4:低点采用最低价、高点采用最高价。若ABS 为0 或省略，则表示相对FLATZIG转向，否则为绝对FLATZIG 转向。与ZIG 函数不同的是本函数返回值在0-1 之间。
例如：FLATZIG(3,5)表示收盘价的5%的归一化ZIG 转向。

GETDX
含义：地线函数取得天地线指标的地线值。

GETTX
含义：天线函数取得天地线指标的天线值。

LFS
含义：长期和短期换手率移动平均线的比例。

LON
含义：以收盘价位置为系数的成交量的累加值；
阐释：
1.当指标曲线向上交叉其平均线时，视为长线买进信号；
2.当指标曲线向下交叉其平均线时，视为长线卖出信号；
3.本指标可搭配MACD、TRIX 指标使用。

LWINNER
含义：LWINNER(5,CLOSE),表示最近5 天的那部分成本以当前收市价卖出的获利盘比例，例如返回0.1 表示10%获利盘，该函数仅对日线分析周期有效。

LWINNERB
含义：LWINNERB(5,CLOSE),表示最近5 天的那部分成本以当前收市价卖出的获利盘比例，例如返回0.1 表示10%获利盘，该函数仅对日线分析周期有效。

MAXCYC
含义：MAXCYC 成本均线指标是个量价均发挥作用的均线，分别代表最大的市场平均建仓成本。

NVI
含义：PVI 指标的理论观点认为，当日的市况如果价跌量缩时，表示大户主导市场。也就是说，PVI 指标主要的功能，在于侦测行情是否属于大户市场。

PEAK 

含义：前M 个ZIG 转向波峰值。
阐释：PEAK(K,N,M,ABS)表示之字转向ZIG(K,N,ABS)的前M个波峰的数值,M 必须大于等于1。若ABS 为0 或省略，则表示相对ZIG 转向，否则为绝对ZIG 转向。
例如：PEAK(1,5,1)表示%5 最高价ZIG 转向的上一个波峰的数值。

PEAKBARS
含义：前M 个ZIG 转向波峰到当前距离。
阐释：PEAKBARS(K,N,M,ABS)表示之字转向ZIG(K,N,ABS)的前M 个波峰到当前的周期数,M 必须大于等于1。若ABS 为0 或省略，则表示相对ZIG 转向，否则为绝对ZIG 转向。
例如：PEAK(0,5,1)表示%5 开盘价ZIG 转向的上一个波峰到当前的周期数。

PPART
含义：PPART(10),表示10 天前的成本占总成本的比例，0.2 表示20%，该函数仅对日线分析周期有效。

PVI
含义：正量指标。

PWINNER
含义：PWINNER(5,CLOSE),表示5 天前的那部分成本以当前收市价卖出的获利盘比例，例如返回0.1 表示10%获利盘，该函数仅对日线分析周期有效。

PWINNERB
含义：PWINNERB(5,CLOSE),表示5 天前的那部分成本以当前收市价卖出的获利盘比例，例如返回0.1 表示10%获利盘，该函数仅对日线分析周期有效。

QIAN
含义：收盘价相对位置的累加值。
阐释：
1.钱龙指标波动于0～1 的间
2.本指标超买超卖界限值随个股不同，使用者应自行调整。
3.一般情形本指标会比股价提早1～2 天抵达高点或低点，因此，指标超买时应慢1～2 天卖出；指标超卖时应慢1～2 天买进。

SAR(N,S,M)
阐释：SAR(N,S,M),N 为计算周期,S 为步长,M 为极值
例如SAR(10,2,20)表示计算10 日抛物转向，步长为2%，极限值为20%。

SARTURN 
阐释：SARTURN(N,S,M),N 为计算周期,S 为步长,M 为极值,若发生向上转向则返回1，若发生向下转向则返回-1，否则为0，其用法与SAR 函数相同。

SHO
含义：以收盘价为系数的成交量的累加值。
阐释：
1.当指标曲线向上交叉其平均线时，视为短线买进信号；
2.当指标曲线向下交叉其平均线时，视为短线卖出信号；
3.本指标可搭配KDJ、DMA 指标使用。

TROUGH
含义：前M 个ZIG 转向波谷值。
阐释：TROUGH(K,N,M,ABS)表示之字转向ZIG(K,N,ABS)的前M 个波谷的数值,M 必须大于等于1。若ABS 为0 或省略，则表示相对ZIG 转向，否则为绝对ZIG 转向。
例如：TROUGH(2,5,2)表示%5 最低价ZIG 转向的前2 个波谷的数值。

TROUGHBARS
含义：前M 个ZIG 转向波谷到当前距离。
阐释：TROUGHBARS(K,N,M,ABS)表示之字转向ZIG(K,N,ABS)的前M 个波谷到当前的周期数,M 必须大于等于1。若ABS 为0 或省略，则表示相对ZIG 转向，否则为绝对ZIG 转向。例如：TROUGH(2,5,2)表示%5 最低价ZIG 转向的前2 个波谷到当前的周期数。

WINNER
阐释：WINNER(CLOSE),表示以当前收市价卖出的获利盘比例，例如返回0.1 表示10%获利盘；WINNER(10.5)表示10.5 元价格的获利盘比例，该函数仅对日线分析周期有效。

WINNERB
阐释：WINNERB(CLOSE),表示以当前收市价卖出的获利盘比例，例如返回0.1 表示10%获利盘；WINNER(10.5)表示10.5 元价格的获利盘比例，该函数仅对日线分析周期有效。

ZHPS
含义：智慧判势函数可以取得智慧判势指标值。
阐释：
1、0线是多空强弱分界线，0线以上为强势，以下为弱势；
2、PSS在0线下上穿PSL，为买入；
3、PSS 在0 线上下穿PSL，为卖出。

ZIG 
阐释：ZIG(K,N,ABS),当价格变化量超过N%时转向,K 表示0:开盘价,1:最高价,2:最低价,3:收盘价,4:低点采用最低价、高点采用最高价。若ABS 为0 或省略，则表示相对ZIG 转向，否则为绝对ZIG 转向。
例如：ZIG(3,5)表示收盘价的5%的ZIG 转向，ZIG(3,0.5,1)表示收盘价的0.5 元绝对ZIG 转向。

ZLSHORT
含义：取得主力进出指标的短线主力值。


8.常识函数
CAPITAL
阐释：流通盘大小，单位为手。对于A 股得到流通A 股，B 股得到B 股总股本，指数返回0。

CATEGORY
阐释：CATEGORY，返回证券类型，指数=0,股票=1,基金=2,债券=3,其它=4,期权=5,外汇=6,期货=7。注意：该函数返回常数。

CURRENTDATE
阐释：CURRENTDATE，返回计算时的日期，该日期是从1900 年开始的日期，例如2003 年1 月1 日表示为1030101。注意：该函数返回常数。

CURRENTTIME
阐释：CURRENTTIME，返回计算时的时间，格式为时分秒，有效范围(000000-235959)。注意：该函数返回常数。

DATATYPE
阐释：DATATYPE，返回数据类型，分时线=1,分笔成交=2,1 分钟线=3,5 分钟线=4,15 分钟线=5,30 分钟线=6,60分钟线=7,日线=8,周线=9,月线=10,多日线=11,年线=12,季线=13,半年线=14。注意：该函数返回常数。

ENTERPOOLPARAM
含义：股票从参数文件引入到股票池时的参数，本函数仅对股票池运算有效，并且当前股票必须是从参数文件引入的有效。

ENTERPOOLPRICE(N)
含义：股票进入股票池时的价格，本函数仅对股票池运算有效。
阐释：ENTERPOOLPRICE(N),取得进入之前第N个状态时的股票价格，参数N=0，表示进入当前状态的价格。

ISSUEDATE
含义：取得发行的年月日。
阐释：ISSUEDATE, 返回发行日期, 有效值范围为19700101-20341231。

MINDIFF
阐释：MINDIFF，返回当前股票价格最小变动量,对于股票是0.01 元，基金是0.001。注意：该函数返回常数。

PRECISION
阐释：PRECISION,返回该股票价格精度，即小数点位数。

PRODUCTID
阐释：PRODUCTID，返回软件的序列号（加密狗号）。注意：该函数返回字符串常数。

TYPE
阐释：TYPE，返回类别:指数为0；A 股为1；B 股为2；债券为3；基金为4；选择权为5;外汇为6;期货为7;期指为8;认购证为9;ETF 为10;LOF 为11;可转债为12;信托为13;权证为14;回购为15.

VOLUNIT
阐释：VOLUNIT，返回每手股数，对于股票值为100，债券为10。注意：该函数返回常数。

DYNAINFO(N),N=3-118

返回动态行情

CAPITAL
阐释：流通盘大小，单位为手。对于A 股得到流通A 股，B 股得到B 股总股本，指数返回0。

CATEGORY
阐释：CATEGORY，返回证券类型，指数=0,股票=1,基金=2,债券=3,其它=4,期权=5,外汇=6,期货=7。注意：该函数返回常数。

CURRENTDATE
阐释：CURRENTDATE，返回计算时的日期，该日期是从1900 年开始的日期，例如2003 年1 月1 日表示为1030101。注意：该函数返回常数。

CURRENTTIME
阐释：CURRENTTIME，返回计算时的时间，格式为时分秒，有效范围(000000-235959)。注意：该函数返回常数。

DATATYPE
阐释：DATATYPE，返回数据类型，分时线=1,分笔成交=2,1 分钟线=3,5 分钟线=4,15 分钟线=5,30 分钟线=6,60分钟线=7,日线=8,周线=9,月线=10,多日线=11,年线=12,季线=13,半年线=14。注意：该函数返回常数。

ENTERPOOLPARAM
含义：股票从参数文件引入到股票池时的参数，本函数仅对股票池运算有效，并且当前股票必须是从参数文件引入的有效。

ENTERPOOLPRICE(N)
含义：股票进入股票池时的价格，本函数仅对股票池运算有效。
阐释：ENTERPOOLPRICE(N),取得进入之前第N个状态时的股票价格，参数N=0，表示进入当前状态的价格。

ISSUEDATE
含义：取得发行的年月日。
阐释：ISSUEDATE, 返回发行日期, 有效值范围为19700101-20341231。

MINDIFF
阐释：MINDIFF，返回当前股票价格最小变动量,对于股票是0.01 元，基金是0.001。注意：该函数返回常数。

PRECISION
阐释：PRECISION,返回该股票价格精度，即小数点位数。

PRODUCTID
阐释：PRODUCTID，返回软件的序列号（加密狗号）。注意：该函数返回字符串常数。

TYPE
阐释：TYPE，返回类别:指数为0；A 股为1；B 股为2；债券为3；基金为4；选择权为5;外汇为6;期货为7;期指为8;认购证为9;ETF 为10;LOF 为11;可转债为12;信托为13;权证为14;回购为15.

VOLUNIT
阐释：VOLUNIT，返回每手股数，对于股票值为100，债券为10。注意：该函数返回常数。

DYNAINFO(N),N=3-118
返回动态行情


9.绘图函数
DRAWBMP 
含义：在图形上绘制位图。
阐释：DRAWBMP(COND,PRICE,BMPFILE),当COND 条件满足时,在PRICE 位置画BMPFILE 文件名指定的BMP 位图(缺省路径为大智慧新一代目录\USERDATA\BMP)。
例如：DRAWBMP(CLOSE>OPEN,LOW,'SUN')表示当收阳时在最低价位置画Superstk\UserData\Bmp\Sun.BMP 位图。

DRAWFLAGTEXT(COND,PRICE,TEXT)
含义：绘制随光标移动的浮动文字。
阐释：DRAWFLAGTEXT(COND,PRICE,TEXT),光标处当COND条件满足时,在PRICE位置用半透明窗口显示文字TEXT,随光标移动而移动。
例如：DRAWFLAGTEXT(CLOSE/OPEN>1.08,LOW,'大阳线')表示当光标移动到涨幅大于8%的地方,在最低价位置显示'大阳线'字样的浮动窗口。

DRAWGBK 
含义：根据条件填充背景区域。
阐释：DRAWGBK(COND,COLOR),填充满足COND 条件的背景区域。COLOR 可以为渐变颜色STRIP，也可以为BMP 图形文件名(缺省路径为大智慧新一代目录\USERDATA\BMP)，若省略COLOR 则使用指标线颜色填充(可以使用COLORRED 等描述符设定)。
例如：DRAWGBK(CLOSE>OPEN),COLORRED；表示收阳时用红色填充背景。DRAWGBK(CLOSE>OPEN,'MyBMP'); 表示收阳时用USERDATA\BMP\MyBMP.BMP 填充背景。

DRAWGBKLAST
含义：图形中最后一根K 线条件填充背景区域。
阐释：DRAWGBKLAST(COND,COLOR),若图形中最后一根K 线满足条件COND，则设定背景COLOR。COLOR 可以为渐变颜色STRIP，也可以为BMP 图形文件名(缺省路径为大智慧新一代目录\USERDATA\BMP)，若省略则使用指标线颜色填充(可以使用COLORRED 等描述符设定)。
例如：DRAWGBKLAST(CLOSE>OPEN),COLORRED;表示最后一根K 线收阳时用红色填充背景。DRAWGBKLAST(CLOSE>OPEN,STRIP(RGB(255,0,0),RGB(0,255,0),1));表示图形中最后一根K 线收阳时红绿过渡色沿水平方向填充背景。

DRAWICON
含义：在图形上绘制小图标。
阐释：
DRAWICON(COND,PRICE,TYPE),当COND 条件满足时,在PRICE 位置画TYPE 号图标，TYPE 取值范围是1—14。
例如：DRAWICON(CLOSE>OPEN,LOW,1)表示当收阳时在最低价位置画1 号图标。

DRAWLINE(COND1,PRICE1,COND2,PRICE2,EXTEND)
含义： 在图形上绘制直线段。
参数：COND1，表示条件变量1；PRICE1，表示第一个画线点，通常取值为H,L,C,O 等等；COND2，表示条件变量2； PRICE2，表示第二个画线点；EXTEND，常数变量。1 向右延伸；2 向左延伸；3 左右同时延伸；

阐释：DRAWLINE(COND1,PRICE1,COND2,PRICE2,EXPAND)，当COND1 条件满足时，在PRICE1 位置画直线起点，当COND2 条件满足时，在PRICE2 位置画直线终点，EXPAND 为延长类型。例如：DRAWILINE(HIGH>=HHV(HIGH,20),HIGH,LOW<=LLV(LOW,20),LOW,1)表示在创20 天新高与创20 天新低之间画直线并且向右延长。

DRAWMOVETEXT(COND,TEXT) 
含义：在窗口指标数值栏目位置绘制随光标处文字。
阐释：DRAWMOVETEXT(COND,TEXT),光标处当COND条件满足时,在窗口顶部绘制文字TEXT,随光标移动而变化。
例如：DRAWMOVETEXT(CLOSE/OPEN>1.08,'大阳线')表示当光标移动到涨幅大于8%的地方,显示'大阳线'字样在窗口顶部。

DRAWRECTABS
含义：在图形绝对位置上画矩形。
阐释：DRAWRECTABS(LEFT,TOP,RIGHT,BOTTOM,COLOR), 以图形窗口(LEFT,TOP)为左上角，(RIGHT,BOTTOM)为右下角绘制矩形，坐标单位是像素,图形窗口左上角坐标为(0,0)，矩形
中间填充颜色COLOR,COLOR 为0 表示不填充。例如：DRAWRECTABS(0,0,100,60,0)表示在图形最左上角位置绘制100*60 像素的矩形，不填充内部。

DRAWRECTREL
含义：在图形相对位置上画矩形。
阐释：DRAWRECTREL(LEFT,TOP,RIGHT,BOTTOM,COLOR), 以图形窗口(LEFT,TOP)为左上角，(RIGHT,BOTTOM)为右下角绘
制矩形，坐标单位是窗口沿水平和垂直方向的1/1000，取值范围是0—999,超出范围则可能显示在图形窗口外,矩形中间填充颜色COLOR,COLOR 为0 表示不填充。
例如：DRAWRECTREL(0,0,500,500,RGB(255,255,0))表示在图形最左上部1/4 位置用黄色绘制矩形。

DRAWTEXT(COND,PRICE,TEXT)
含义：在图形上显示文字。
参数： COND, 表示条件变量1；PRICE，表示标识文字的位置；TEXT，所写的文字内容,但是用单引号引入；
阐释：DRAWTEXT(COND,PRICE,TEXT)，当COND 条件满足时，在PRICE 位置书写文字TEXT。
例如：DRAWTEXT(CLOSE/OPEN>1.08,LOW,'大阳线')表示当日涨幅大于8%时在最低价位置显示'大阳线'字样。 

DRAWTEXTABS
含义：在图形绝对位置上显示文字。
阐释：DRAWTEXTABS(X,Y,TEXT),在图形窗口(X,Y)坐标位置书写文字TEXT，坐标单位是像素,图形窗口左上角坐标为(0,0)。
例如：DRAWTEXTABS(0,0,'注意')表示在图形最左上角位置显示'注意'字样。

DRAWTEXTREL
含义：在图形相对位置上显示文字。
阐释：DRAWTEXTREL(X,Y,TEXT),在图形窗口(X,Y)坐标位置书写文字TEXT，坐标单位是窗口沿水平和垂直方向的1/1000，X,Y取值范围是0—999,超出范围则可能显示在图形窗口外。
例如：DRAWTEXTREL(0,0,'注意')表示在图形最左上角位置显示'注意'字样。

FILLRGN 
阐释：FILLRGN(PRICE1,PRICE2,COND1,COLOR1,COND2,COLOR2...),填充PRICE1 到PRICE2 之间的区域，当COND1条件满足时,用COLOR1 颜色，当COND2 条件满足时,用COLOR2 颜色，否则不填充，从COND1 之后的参数均可以省略，最多可以有10 组条件。
例如：FILLRGN(CLOSE,OPEN,CLOSE>OPEN,RGB(255,0,0),CLOSE

FLOATRGN
含义：根据条件填充区域
阐释：FLOATRGN(PRICE,WIDTH,COND1,COLOR1,COND2,COLOR2...),以PRICE 为基础填充宽度为WIDTH 像素的区域，WIDTH 为负则向下填充,当COND1 条件满足时,用COLOR1颜色，当COND2 条件满足时,用COLOR2 颜色，否则不填充，从COND1 之后的参数均可以省略，最多可以有10 组条件。
例如：FLOATRGN(CLOSE,VOL/HHVALL(VOL)*15,CLOSE>OPEN,RGB(255,0,0),1,RGB(0,255,0))表示沿收盘价填充宽度为成交量的区域，区域最大宽度为15 像素,阳线时用红色，阴线时用绿色。

FLOATSTICK
含义：根据条件绘制浮动柱状线。
阐释：FLOATSTICK(PRICE,WIDTH,COND1,COLOR1,COND2,COLOR2...),以PRICE 为基础绘制长度为WIDTH 像素的柱状线，WIDTH 为负则向下绘制,当COND1 条件满足时,用COLOR1颜色，当COND2 条件满足时,用COLOR2 颜色，否则不绘制，从COND1 之后的参数均可以省略，最多可以有10 组条件。
例如：FLOATSTICK(CLOSE,VOL/HHVALL(VOL)*20)表示沿收盘价绘制宽度为成交量增量的柱状线,柱状线最大宽度20像素。

HORILINE(COND1,PRICE,COND2,TOLEFT)
含义：在图形上绘制水平线段。
阐释：HORILINE(COND1,PRICE,COND2,TOLEFT),当COND1条件满足时,在PRICE位置画水平线，直到COND2条件满足为止,TOLEFT为1表示向左边绘制，TOLEFT参数可以省略，表示0。
例如：HORILINE(HIGH>=HHV(HIGH,20),HIGH,HIGH>=HHV(HIGH,20))表示在创20天新高与下一次创20天新高之间画水平线段。

PARTLINE(PRICE,COND1,COLOR1,COND2,COLOR2...)
含义：根据条件画线。
阐释：PARTLINE(PRICE,COND1,COLOR1,COND2,COLOR2...), 绘制PRICE 线，当COND1 条件满足时,用COLOR1 颜色，当COND2 条件满足时,用COLOR2 颜色，否则不绘制， 从COLOR1 之后的参数均可以省略，最多可以有10 组条件。
例如：PARTLINE(CLOSE,CLOSE>OPEN,RGB(255,0,0),CLOSE


PERCENTBAR(P1,P2)
阐释：用百分比柱绘制指标线。
用法:PERCENTBAR(P1,P2),绘制柱状线，P1表示柱状线高度，P2表示对比量高度，二者均必须在0-100之间；当P2例如：PERCENTBAR(VOL/CAPITAL*100,REF(VOL/CAPITAL,1)*100)绘制换手率柱状线,并且绘制换手率增量。


POLYLINE (COND,PRICE) 
含义： 在图形上绘制折线段。
参数：COND，表示条件变量1；PRICE，表示绘图点的位置；
阐释：POLYLINE(COND,PRICE)，当COND 条件满足时，以PRICE 位置为顶点画折线连接。
例如：POLYILINE(HIGH>=HHV(HIGH,20),HIGH)表示在创20 天新高点之间画折线。

RGB
阐释：RGB(R,G,B),表示用三原色红(R)绿(G)蓝(B)混合组成指定颜色,每种颜色值可以设定为0-255。例如RGB(255,0,0)表示红色，RGB(0,0,255)表示蓝色.

STICKLINE(COND,PRICE1,PRICE2,WIDTH,ATTR)
含义： 在图形上绘制柱线。
参数：COND， 表示条件变量1；PRICE1，表示第一绘图点的位置；PRICE2，表示第二绘图点的位置；WIDTH，表示所绘制的柱线的宽度,取值的范围0-9,宽度依次递增，取0 时为一条线,在大智慧中的主图K 线的柱宽为8；ATTR，ATTR 的个位不为0 则画空心柱，ATTR 的十位以上部分表示左右移动，范围是-1000—1000，表示移动位置的千分比。
阐释：当COND 条件满足时,在PRICE1 和PRICE2 位置之间画柱状线，宽度为WIDTH(10 为标准间距), ATTR 的个位不为0 则画空心柱，ATTR 的十位以上部分表示左右移动，范围是-1000—1000，表示移动位置的千分比。
例如：STICKLINE(CLOSE>OPEN,CLOSE,OPEN,0.8,501) 表示画K线中阳线的空心柱体部分,向右移动K 线宽度的50%。

STRIP
阐释：STRIP(RGB1,RGB2,DIR),表示生成RGB1 色到RGB2 色的渐变区域，DIR=1 表示沿水平方向，否则表示沿垂直方向,其中
RGB1,RGB2 必须用RGB 函数描述。本函数只能用于DRAWGBK、DRAWGBKLAST 函数中，作为背景填充色描述符。
例如：STRIP(RGB(255,0,0),RGB(0,255,0),0)表示由红色到绿色的垂直渐变色。

TIPTEXT
含义：在图形上显示图标，鼠标移近时显示文字。
阐释：TIPTEXT(COND,PRICE,TEXT), 当COND 条件满足时, 在PRICE 位置显示图标，若PRICE 为0，则在图形底部显示图标。
例如： TIPTEXT(CLOSE/OPEN>1.08,LOW,' 大阳线， 股价为:' close)表示当日涨幅大于8%时在最低价位置显示图标,鼠标移近时显示文字'大阳线,股价为:15.88'字样。

 VERTLINE
含义：在图形上绘制垂直线。
阐释：VERTLINE(COND,TYPE),当COND 条件满足时,沿垂直方向绘制TYPE 类型的线段,TYPE=0 表示实线，1 表示虚线'---'，2 表示点线'...'，3 表示点划线'-.-.-'，4 表示点点划线'-..-..-'。
例如：VERTLINE(HIGH>=HHV(HIGH,20),1)表示在创20 天新高画垂直虚线。


10.财务函数
PFFIN(N,M)
阐释: 表示取得M 个报告期之前的第N 号专业财务数据,例如PROFFIN(3001,0)表示最近一期总股本，N 的取值请参阅下表。

PFFININ (N,Y,MD)
阐释: 表示取得Y 年M 月D 日的第N 号专业财务数据，若当天未发布财务数据则为0，如PFFININ(3001,2000,1231)取得2000年12 月31 日的总股本，N 的取值请参阅下面列表。

PFFINLAST(N)
阐释:表示最近报告期的第N 号专业财务数据距离现在的周期数，N 的取值请参阅下面列表。

PFFINON(N,Y,MD)
阐释: 表示取得Y 年M 月D 日的第N 号专业财务数据，若当天未发布财务数据则使用之前最近一期的数据， 如PFFINON(3001,2000,0101)取得2000 年1 月1 日或之前最近一期的总股本，N 的取值请参阅下面列表。

PFFINTERM(M)
阐释: 表示取得M 个报告期之前的财务报表是年报、中报还是季报返回1：第一季度季报，2：中报，3：第三季度季报，4：年报。

PFSTR(N,M)
阐释: 表示取得M个报告期之前的第N 号专业财务字符串数据,例如：PFSTR(5001,0)表示最近一期第一大股东名称，N 的取值请参阅上表。

PFSTRIN(N,Y,MD)
阐释: 表示取得Y 年M 月D 日的第N 号专业财务数据，若当天未发布财务数据则为0，如PFSTRIN(5001,2000,1231)取得2000 年12 月31 日的第一大股东名称，N 的取值请参阅下面列表。

PFSTRON(N,Y,MD)
阐释:表示取得Y 年M 月D 日的第N 号专业财务数据，若当天未发布财务数据则使用之前最近一期的数据， 如PFSTRON(5001,2000,0101)取得2000 年1 月1 日或之前最近一期的第一大股东名称，N 的取值请参阅下面列表。

SETPFFIN(X)
阐释:表示将专业财务数据属性设定为X,X 从低到高每一位表示一个含义。
第1 位：包含年报;
第2 位：包含中报;
第3 位：包含季报;
第4 位：包含最新财务指标;
第5 位：调整中报、季报财务指标;
例如：SETPFFIN(01011)表示取得年报、中报，最新一期数据无论是那个报告期都要包括在内，中报季报不作调整；如果不调用本函数，系统默认值为01111注意:在本函数后的其它专业财务函数将受到本次属性设置的影响。


11.字符串函数
BKNAME
含义:该函数返回一个股票所属的板块名称。

DATESTR(Date)
含义:日期数据到字符串转换函数。
阐释:DATESTR(Date);该函数返回一个字符串。

EXTRASTRING(S)
含义:取得附加字符串数据。
用法:EXTRASTRING(S),取得名为S的附加字符串数据。

F10FIND(S,N)
含义:当前股票的F10资料中查找字符串。
阐释:F10FIND(S,N),将从当前股票的F10资料的第N个字符开始查找字符串S，返回找到的位置，返回-1表示未找到。
例如：F10FIND('发行价',1),将从F10资料的第1个字符开始查找字符串'发行价'，返回找到的位置。

F10TEXT(N,M)
含义:取得当前股票的F10资料中指定范围的子字符串。
阐释:F10TEXT(N,M),将得到当前股票的F10资料中从第N个字符开使的M个字节长的字符串，M=0表示一直取到行尾,M<0表示之后的全部字符。
例如：F10TEXT(30,10),将得到F10资料中从第30个字符开使的10个字节长的字符串。

INBLOCK(S)
含义:判断本股票是否板块成员。
阐释:INBLOCK(S),若本股票是板块S的成员将返回1，否则返回0，
例如：INBLOCK('工业板块'),若本股票属于工业板块则返回1。

LOWERSTR(STR)
含义:将字符串转换为小写。
阐释:LOWERSTR(STR),将返回STR对应的小写字符串。
例如：LOWERSTR('EFgh')将返回'efgh'。

MARKETLABEL
含义:取得当前股票的市场代码。
阐释:MARKETLABEL,将返回当前股票的市场代码，例如：沪市返回'SH',深市返回'SZ'。

MARKETNAME
含义:取得当前股票的市场名称。
阐释:MARKETNAME,将返回当前股票的市场名称，
例如：沪市返回'上海证券交易所'，深市返回'深圳证券交易所'。

NUMTOSTR(N)
含义:将数字转化为字符串。
阐释:NUMTOSTR(N),将N转化为字符串返回，精确到小数点后三位,但若结尾为0将被删除。
例如：NUMTOSTR(close)将返回收盘价对应的字符串，例如'15.78'。

NUMTOSTRN(N,M)
含义:将数字转化为字符串，用户可以设定精度。
阐释:NUMTOSTRN(N,M),将N转化为字符串返回，精确到小数点后M位。
例如：NUMTOSTRN(close,5)将返回收盘价对应的字符串，例如'15.78000'。

SELFSTRING(S)
含义:取得自定义字符串数据。
阐释:SELFSTRING(S),取得名为S的自定义字符串数据。

STKLABEL
含义:取得股票代码。
阐释:STKLABEL,将返回当前股票的代码,例如深发展A将返回'000001'。

STKNAME
含义:取得股票名称。
阐释:STKNAME,将返回当前股票的名称。

STR(N)
含义:将数字转化为字符串。
阐释:STR(N),将N转化为字符串返回，精确到小数点后三位,但若结尾为0将被删除。
例如：STR(close)将返回收盘价对应的字符串，例如'15.78'。

STRCMP(STR1,STR2)
含义:字符串比较。
阐释:STRCMP(STR1,STR2),若STR1>STR2则返回1，STR1例如：STRCMP('abcdef','abc')返回1。

STRFIND(STR,S1,N)
含义:在字符串中查找另一个字符串。
阐释:STRFIND(STR,S1,N),从字符串STR的第N个字符开始查找字符串S1,返回找到的位置，若没有找到就返回0。
例如：STRFIND('abcdefgh','cde',1)返回3。

STRLEFT(STR,N)
含义:取得字符串的左边部分。
阐释:STRLEFT(STR,N),返回字符串STR的左边N个字符。
例如：STRLEFT('abcdef',3)得到'abc'。

STRMID(STR,N,M) 
含义:取得字符串的中间部分。
阐释:STRMID(STR,N,M),返回字符串STR的第N个字符开始的长度为M个字符的字符串。
例如：STRMID('abcdef',3,3)得到'cde'。

STRRIGHT(STR,N)
含义:取得字符串的右边部分。
阐释:STRRIGHT(STR,N),返回字符串STR的右边N个字符。
例如：STRRIGHT('abcdef',3)得到'def'。

STRTONUM(STR)
将字符串转化为数字。
阐释:STRTONUM(STR),将STR转化为数字返回。
例如：STRTONUM('12.5')将返回数值12.5。

TYPESTR 
阐释:股票类别(文本)
该函数返回一个字符串：'指数'、'A股'、'B股'、'基金'、'债券'、'选择权'、'外汇'、'期货'、'期指'、'认购证'、'ETF'、'LOF'、'可转债'、'信托'、'权证'、'回购'等。

UPPERSTR(STR)
阐释:将字符串转换为大写。将返回STR对应的大写字符串。
例如：UPPERSTR('abcd')将返回'ABCD'。

WARNING(STR,C)
每次开机后第一次运行该公式，弹出警示窗口提示用户。
阐释:WARNING(STR,C),当满足C条件时，弹出警示窗口显示字符串STR，若STR以http://开头，则显示所指引的网页。
例如WARNING('http://www.gw.com.cn',close>open)。

12.协方差函数
BETA(N) 
含义:贝塔系数。
阐释:BETA(N)为当前股票收益与大盘收益相比的贝塔系数，该系数表明大盘每变动1%,则该股票将变动N%。
例如:BETA(10)表示10周期贝塔系数。

BETA2(X,Y,N)
含义:两样本的相关放大系数。
阐释:BETA2(X,Y,N)为X与Y的N周期相关放大系数，表示Y变化1%，则X将变化N%。
例如:BETA2(CLOSE,INDEXC,10)表示收盘价与大盘指数之间的10周期相关放大率。

BETADOWN(N)
含义:下跌贝塔系数。
阐释:BETADOWN(N)为当前股票收益与大盘收益相比的下跌贝塔系数，该系数表明大盘每下跌1%,则该股票将下跌N%。
例如:BETADOWN(10)表示10周期下跌贝塔系数。

BETAUP(N)
含义:上涨贝塔系数。
阐释:BETAUP(N)为当前股票收益与大盘收益相比的上涨贝塔系数，该系数表明大盘每上涨1%,则该股票将上涨N%。
例如:BETAUP(10)表示10周期上涨贝塔系数。

COVAR(X,Y,N)
含义:两样本的协方差。
阐释:COVAR(X,Y,N)为X与Y的N周期协方差。
例如:COVAR(CLOSE,INDEXC,10)表示收盘价与大盘指数之间的10周期协方差。

RELATE(X,Y,N)
含义:两样本的相关系数。
阐释:RELATE(X,Y,N)为X与Y的N周期相关系数，其有效值范围在-1 — 1之间。
例如:RELATE(CLOSE,INDEXC,10)表示收盘价与大盘指数之间的10周期相关系数。


13.交易系统函数
AVGENTERPRICE
含义:交易系统中当前持有股票的平均买入成本,该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

BESTPERCENT
含义:交易系统中当前位置之前所有交易中利润率最大一次的利润率，该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

BESTTRADE
含义:交易系统中当前位置之前所有交易中盈利最大一次的利润额。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

BUY(V,Type,P)
含义:买入，本函数仅能用于交易系统。
阐释:表示买入V股当前股票，Type表示买入类型，P表示买入价格，所有参数均可以省略。
V:买入股数或买入资金百分比(N%)，省略表示100%；
Type:可以是本周期收盘(THISCLOSE),次周期开盘(MARKET),次周期限价单(LIMIT)，次周期停损单(STOP)；
P:对于限价单、停损单需要指定的买入价格。

BUYSHORT(V,Type,P)
含义:空头买入，本函数仅能用于交易系统。
阐释:表示空头买入V股当前股票，Type表示买入类型，P表示买入价格，所有参数均可以省略。
V:买入股数或买入资金百分比(N%)，省略表示100%；
Type:可以是本周期收盘(THISCLOSE),次周期开盘(MARKET),次周期限价单(LIMIT)，次周期停损单(STOP)；
P:对于限价单、停损单需要指定的买入价格。

CASH
含义:交易系统中当前的现金存量。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

ENTERBARS
含义:交易系统中上次买入到当前的周期数。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

ENTERPRICE
含义:交易系统中上次交易的买入价格。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

EXITBARS
含义:交易系统中上次卖出到当前的周期数。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

EXITPRICE
含义:交易系统中上次交易的卖出价格。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

HOLDING
含义:交易系统中当前持有的股票股数。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

LIMIT
含义:交易方式控制符。
阐释:LIMIT,加入限价单，次周期达到限价即操作，否则放弃。所谓限价就是股价优于设定的价格，具体说来对于买入或卖空就是低于设定价格，对于卖出或买空就是高于设定价格。例如：Buy(1000,LIMIT,CLOSE 0.1)。

MARKET
含义:交易方式控制符。
阐释:MARKET,按照次周期开盘价操作。例如：Buy(1000,MARKET)。

MAXSEQLOSS
含义:交易系统中当前位置之前连续亏损交易的最大次数。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

MAXSEQWIN
含义:交易系统中当前位置之前连续盈利交易的最大次数，该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

NUMLOSSTRADE
含义:交易系统中当前位置之前总共有多少次亏损的交易，注意每次卖出算一次交易，而买入不算。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

NUMSEQLOSS
含义:交易系统中当前位置之前连续有多少次亏损的交易，注意每次卖出算一次交易，而买入不算。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

NUMSEQWIN
含义:交易系统中当前位置之前连续有多少次盈利的交易，注意每次卖出算一次交易，而买入不算。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

NUMWINTRADE
含义:交易系统中当前位置之前总共有多少次盈利的交易，注意每次卖出算一次交易，而买入不算。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

OPENPROFIT
含义:交易系统中当前浮动盈亏（当前持仓市值与持仓成本之差）。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

PERCENTWIN
含义:交易系统中当前位置之前盈利交易占总交易次数的比例，其数值在0—1之间。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

SELL(V,Type,P)
含义:卖出，本函数仅能用于交易系统。
阐释:表示卖出V股当前股票，Type表示卖出类型，P表示卖出价格，所有参数均可以省略。
V:卖出股数或卖出持仓百分比(N%)，省略表示100%；
Type:可以是本周期收盘(THISCLOSE),次周期开盘(MARKET),次周期限价单(LIMIT)，次周期停损单(STOP)；
P:对于限价单、停损单需要指定的卖出价格。

SELLSHORT(V,Type,P)
含义:空头卖出，本函数仅能用于交易系统。
阐释:表示空头卖出V股当前股票，Type表示卖出类型，P表示卖出价格，所有参数均可以省略。
V:卖出股数或卖出持仓百分比(N%)，省略表示100%；
Type:可以是本周期收盘(THISCLOSE),次周期开盘(MARKET),次周期限价单(LIMIT)，次周期停损单(STOP)；
P:对于限价单、停损单需要指定的卖出价格。

STOP
含义:交易方式控制符。
阐释:STOP,加入停损单，次周期达到设定价格即操作买入，否则放弃。
所谓停损就是股价比设定的价格要差，具体说来对于买入或卖空就是高于设定价格，对于卖出或买空就是低于设定价格
例如：Buy(1000,STOP,CLOSE-0.01)。

THISCLOSE
含义:交易方式控制符。
阐释:THISCLOSE,按照本周期收盘价操作。例如：Buy(1000,THISCLOSE)。

TOTALTRADE
含义:交易系统中当前位置之前总共有多少次交易，注意每次卖出算一次交易，而买入不算。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

WORSTPERCENT
含义:交易系统中当前位置之前所有交易中亏损率最大一次的利润率该函数仅在使用Buy,Sell新交易函数的交易系统中有效。

WORSTTRADE
含义:交易系统中当前位置之前所有交易中亏损最大一次的亏损额。该函数仅在使用Buy,Sell新交易函数的交易系统中有效。


14.股票池函数
ENTERPOOLPARAM
含义：股票从参数文件引入到股票池时的参数，本函数仅对股票池运算有效，并且当前股票必须是从参数文件引入的有效。

ENTERPOOLPRICE(N)
含义：股票进入股票池时的价格，本函数仅对股票池运算有效。
阐释：ENTERPOOLPRICE(N),取得进入之前第N个状态时的股票价格，参数N=0，表示进入当前状态的价格。

ENTERPOOLBARS(N)
含义：股票进入股票池到现在的周期数，本函数仅对股票池运算有效。
阐释:ENTERPOOLBARS(N),取得股票进入之前第N个状态到目前的周期数，参数N=0，表示进入当前状态到目前的周期数。

15.输出修饰符函数
ALIGNx
含义:水平对齐方式控制符。
阐释:ALIGNx,用逗号隔开放置在DrawIcon,DrawBmp,DrawText语句后，用于设定对齐方式，x=0表示左对齐，1表示中对齐，2表示右对齐，缺省左对齐。
例如：DrawIcon(CLOSE>OPEN,LOW,6),ALIGN1;表示绘制6号图标，采用中对齐。

CIRCLEDOT
含义:输出线型控制符。
阐释:CIRCLEDOT,用逗号隔开放置在输出语句后，将该输出绘制成小圆圈线。例如：VOL,CIRCLEDOT;

COLORbbggrr
含义:输出颜色控制符。
阐释:COLORbbggrr,用逗号隔开放置在输出语句后，用于设定绘制指标线的颜色,rr、gg、bb分别表示红绿蓝三色的份量，用十六进制表示，00表示最小，FF表示最大。
例如：CLOSE,COLOR0000FF;表示用红色绘制收盘价线。

COLOR3D
含义:输出线型控制符。
阐释:COLOR3D,用逗号隔开放置在输出语句后，将该输出绘制成阴阳3D柱线，若数值正则用阳线颜色，否则用阴线颜色。
例如：VOL,COLOR3D;表示绘制成交量阴阳3D柱线。

COLORSTICK
含义:输出线型控制符。
阐释:COLORSTICK,用逗号隔开放置在输出语句后，将该输出绘制成阴阳颜色柱线，若数值正则用阳线颜色，否则用阴线颜色。例如：CLOSE-CLOSE[1],COLORSTICK;表示绘制涨跌阴阳柱线。

CROSSDOT
含义:输出线型控制符。
阐释:CROSSDOT,用逗号隔开放置在输出语句后，将该输出绘制成斜叉线。例如：VOL,CROSSDOT;

DASHLINE
含义:输出线型控制符。
阐释:DASHLINE,用逗号隔开放置在输出语句后，将该输出绘制成长虚线。例如：VOL,DASHLINE;

DOTLINE
含义:输出线型控制符。
阐释:DOTLINE,用逗号隔开放置在输出语句后，将该输出绘制成虚线。例如：VOL,DOTLINE;

LAYERx
含义:输出层控制符。
阐释:LAYERx,用逗号隔开放置在输出语句后，用于设定绘制指标线所在层,x可以为0-7，数字越小表示越靠上层，将会遮盖其下层的图形，缺省为第4层。
例如：CLOSE,LAYER0;表示绘制收盘价线在第0层，将遮盖所有其它层。

LINESTICK
含义:输出线型控制符。
阐释:LINESTICK,用逗号隔开放置在输出语句后，将该输出绘制成柱线，同时带上包络。
例如：VOL,LINESTICK;表示绘制成交量包络柱线。

LINETHICKx
含义:输出线宽控制符。
阐释:LINETHICKx,用逗号隔开放置在输出语句后，用于设定绘制指标线宽度,x可以为0-7，若设为0则表示不显示。
例如：CLOSE,LINETHICK6;表示用6点粗线绘制收盘价线。

MOVEx
含义:向后平移控制符。
阐释:MOVEx,用逗号隔开放置在输出语句后，用于设定输出线向后平移x周期，x=0—255。
例如：CLOSE,MOVE10;表示绘制收盘价线向后移动10周期。

POINTDOT
含义:输出线型控制符。
阐释:STICK,用逗号隔开放置在输出语句后，将该输出绘制成点线。例如：VOL,POINTDOT;

PRECISx 
含义:输出精度控制符。
阐释:PRECISx,用逗号隔开放置在输出语句后，用于设定指标数值精确到小数点后多少位,x可以为0-6。
例如：CLOSE,PRECIS6;表示收盘价线显示6位小数精度。

STICK
含义:输出线型控制符。
阐释:STICK,用逗号隔开放置在输出语句后，将该输出绘制成柱线。
例如：VOL,STICK;表示绘制成交量柱线。

STICK3D
含义:输出线型控制符。
阐释:STICK3D,用逗号隔开放置在输出语句后，将该输出绘制成3D柱线。
例如：VOL,STICK3D;表示绘制成交量3D柱线。

VALIGNx
含义:垂直对齐方式控制符。
阐释:VALIGNx,用逗号隔开放置在DrawIcon,DrawBmp,DrawText语句后，用于设定对齐方式，x=0表示上对齐，1表示中对齐，2表示下对齐，缺省上对齐。
例如：DrawIcon(CLOSE>OPEN,LOW,6),ALIGN1,VALIGN0;表示绘制6号图标，采用中对齐和上对齐。

VOLSTICK
含义:输出线型控制符。
阐释:VOLSTICK,用逗号隔开放置在输出语句后，将该输出绘制成与K线同样粗细的柱线。
例如：VOL,VOLSTICK;表示绘制成交量柱线。

15.其他函数
SET(Type,V)
含义:设置统计计算参数。
阐释:表示将参数Type设置为数值V,若不设置相当于V等于0。参数Type可以为：
ABS_HHV:  表示HHV,LLV,HHVBARS,LLVBARS等函数计算结果的有效开始位置，0表示从第一个统计数据开始，1表示必须统计满N周期。
HORI_PCB: 表示水平绘制横向指标柱状图。

STKINDI('STKNAME','INDINAME',PARAMs)
含义:引用指定股票的指标数值。
阐释:STKINDI('STKNAME','INDINAME',PARAMs)，STKNAME是股票代码，INDINAME是指标及其指标线名称，其语法和双引号引用指标数值相同，PARAM是可选参数，其数量应当等于被应用指标的参数数量，否则将使用缺省参数数值。
例如:
STKINDI('SZ000001','MA.MA1',5,10,15,20)表示引用深发展的MA指标的MA1指标线，计算参数5，10，15，20。
STKINDI('000001','KDJ.K#WEEK',9,4,4,)表示引用深发展的周线KDJ指标的K指标线，计算参数9,4,4。
STKINDI('SH600000','EXPLORER.KDJ#MONTH')表示引用浦发银行的KDJ条件选股月线公式，计算使用默认值。






















































