import pandas as pd
import numpy as np
import talib
from MyTT import MACD as MyTT_MACD
from MyTT import RSI as MyTT_RSI
import matplotlib.pyplot as plt
import multiprocessing
import os
from datetime import datetime
import matplotlib
import matplotlib.dates as mdates
import mplfinance as mpf  # 添加 mplfinance 库用于绘制蜡烛图
from heikin_ashi_scorer import HeikinAshiScorer

matplotlib.use('TkAgg')  # 设置后端为 Agg，避免显示图形


def FORCAST(X, N):
    """
    线性回归预测值，求X的N周期线性回归预测值
    
    参数:
    X: 数组, 计算样本
    N: 整数, 计算周期数
    
    返回:
    数组: X的N周期线性回归预测值
    """
    result = np.full_like(X, np.nan, dtype=float)

    # 确保X是numpy数组
    X = np.asarray(X)

    # 对每个窗口计算线性回归预测值
    for i in range(N - 1, len(X)):
        # 取N个周期的数据
        y = X[i - N + 1:i + 1]
        # 创建x坐标 (0, 1, 2, ..., N-1)
        x = np.arange(N)

        # 计算线性回归系数 (斜率和截距)
        slope, intercept = np.polyfit(x, y, 1)

        # 预测下一个值 (在x=N处)
        result[i] = slope * N + intercept

    return result


def ZLSMA(close, N=32):
    """
    零延迟最小二乘均线 (Zero Lag Least Squares Moving Average)
    
    参数:
    close: 数组, 收盘价
    N: 整数, 计算周期数, 默认32
    
    返回:
    数组: ZLSMA值
    """
    # 计算一阶LSMA (对收盘价的线性回归预测)
    lsma = FORCAST(close, N)

    # 计算二阶LSMA (对一阶LSMA的线性回归预测)
    lsma2 = FORCAST(lsma, N)

    # 计算误差项
    eq = lsma - lsma2

    # 计算ZLSMA
    zlsma = lsma + eq

    return zlsma


def calculate_heikin_ashi(df):
    """
    计算 Heikin Ashi 蜡烛图数据，传统实现:
    
    haClose = (open + high + low + close) / 4
    haOpen = 第一个值使用 (open + close) / 2，其余值使用 (前一个haOpen + 前一个haClose) / 2
    haHigh = max(high, haOpen, haClose)
    haLow = min(low, haOpen, haClose)
    
    参数:
    df: DataFrame, 包含 open, high, low, close 列的数据框
    
    返回:
    DataFrame: 包含 ha_open, ha_high, ha_low, ha_close 列及原始数据框中的所有列
    """
    df = df.copy()

    # 确保列名正确
    required_cols = ['open', 'high', 'low', 'close', 'Time']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"输入数据缺少必要的列: {col}")

    # 调试: 打印排序前的数据
    print("\n[DEBUG] 排序前的数据前5行:")
    print(df[['Time', 'open', 'high', 'low', 'close']].head())
    
    # 检查是否所有时间戳都相同
    unique_times = df['Time'].nunique()
    print(f"\n[DEBUG] 数据中有 {unique_times} 个不同的时间戳")
    
    # 如果时间戳相同，添加序列号作为辅助排序键
    if unique_times == 1 or unique_times < len(df) * 0.1:  # 如果不同时间戳数量很少
        print("[DEBUG] 检测到时间戳信息不完整，添加序列号作为辅助排序键")
        df['sequence'] = range(len(df))
    
    # 确保按时间排序并重置索引
    if 'sequence' in df.columns:
        df = df.sort_values(['Time', 'sequence']).reset_index(drop=False)  # 保留原始索引
    else:
        df = df.sort_values('Time').reset_index(drop=False)  # 保留原始索引

    # 调试: 打印排序后的数据
    print("\n[DEBUG] 排序后的数据前5行:")
    print(df[['Time', 'open', 'high', 'low', 'close']].head())
    print(f"\n[DEBUG] 总共有 {len(df)} 条5分钟K线数据")

    # 创建 Heikin Ashi 数据框
    ha_df = pd.DataFrame(index=df.index)

    # 计算 Heikin Ashi 收盘价
    ha_df['ha_close'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4
    
    # 打印前5个HA收盘价计算过程
    print("\n[DEBUG] 前5个HA收盘价计算过程:")
    for i in range(min(5, len(df))):
        print(f"序号: {i}, 时间: {df.iloc[i]['Time']}, 开盘: {df.iloc[i]['open']:.2f}, 最高: {df.iloc[i]['high']:.2f}, "
              f"最低: {df.iloc[i]['low']:.2f}, 收盘: {df.iloc[i]['close']:.2f}, "
              f"HA收盘 = ({df.iloc[i]['open']:.2f} + {df.iloc[i]['high']:.2f} + {df.iloc[i]['low']:.2f} + {df.iloc[i]['close']:.2f}) / 4 = {ha_df.iloc[i]['ha_close']:.2f}")

    # 初始化 Heikin Ashi 开盘价数组
    ha_open = np.zeros(len(df))

    # 第一个值使用 (open + close) / 2
    ha_open[0] = (df.iloc[0]['open'] + df.iloc[0]['close']) / 2
    print(f"\n[DEBUG] 第一个K线 (序号: 0) 的 HA_Open: {ha_open[0]:.2f} = ({df.iloc[0]['open']:.2f} + {df.iloc[0]['close']:.2f}) / 2")

    # 逐行计算其余值
    for i in range(1, len(df)):
        # 使用前一个HA_Open和前一个HA_Close计算当前HA_Open
        ha_open[i] = (ha_open[i - 1] + ha_df.iloc[i - 1]['ha_close']) / 2
        
        # 打印前5个计算过程
        if i < 5:
            print(f"序号: {i}, 时间: {df.iloc[i]['Time']}, HA_Open: {ha_open[i]:.2f} = ({ha_open[i-1]:.2f} + {ha_df.iloc[i-1]['ha_close']:.2f}) / 2")

    # 将计算结果赋值给 DataFrame
    ha_df['ha_open'] = ha_open

    # 逐行计算 Heikin Ashi 最高价和最低价，确保正确使用当前行的原始数据
    ha_high = np.zeros(len(df))
    ha_low = np.zeros(len(df))

    print("\n[DEBUG] 前5个HA最高价和最低价计算过程:")
    for i in range(len(df)):
        # 计算当前行的 HA 最高价和最低价
        curr_high = df.iloc[i]['high']
        curr_low = df.iloc[i]['low']
        curr_ha_open = ha_open[i]
        curr_ha_close = ha_df.iloc[i]['ha_close']

        # HA最高价 = MAX(当前原始最高价, 当前HA开盘价, 当前HA收盘价)
        ha_high[i] = max(curr_high, curr_ha_open, curr_ha_close)

        # HA最低价 = MIN(当前原始最低价, 当前HA开盘价, 当前HA收盘价)
        ha_low[i] = min(curr_low, curr_ha_open, curr_ha_close)
        
        # 打印前5个计算过程
        if i < 5:
            print(f"序号: {i}, 时间: {df.iloc[i]['Time']}, HA_High: {ha_high[i]:.2f} = max({curr_high:.2f}, {curr_ha_open:.2f}, {curr_ha_close:.2f})")
            print(f"序号: {i}, 时间: {df.iloc[i]['Time']}, HA_Low: {ha_low[i]:.2f} = min({curr_low:.2f}, {curr_ha_open:.2f}, {curr_ha_close:.2f})")

    # 将计算结果赋值给 DataFrame
    ha_df['ha_high'] = ha_high
    ha_df['ha_low'] = ha_low

    # 打印前5行完整的HA数据
    print("\n[DEBUG] 前5行完整的Heikin Ashi数据:")
    print(ha_df[['ha_open', 'ha_high', 'ha_low', 'ha_close']].head(5))
    
    # 如果添加了序列号，现在可以删除它
    if 'sequence' in df.columns:
        df = df.drop('sequence', axis=1)

    # 将原始数据列也添加到结果中
    for col in df.columns:
        if col not in ha_df.columns and col != 'index':
            ha_df[col] = df[col]

    # 恢复原始索引
    original_index = df['index']
    ha_df = ha_df.reset_index(drop=True)
    ha_df.index = original_index

    return ha_df


def process_stock_data(df):
    """
    处理股票数据，删除 suspendFlag 非0的行
    
    参数:
    df: DataFrame, 原始股票数据
    
    返回:
    DataFrame: 处理后的数据（删除 suspendFlag 非0的行）
    """
    # 使用Polars的列操作进行数据过滤
    import polars as pl
    pl_df = pl.from_pandas(df)
    # 如果 suspendFlag 列为字符串，先转换为整型再比较
    pl_df = pl_df.with_column(pl.col("suspendFlag").cast(pl.Int32))
    valid_data = pl_df.filter(pl.col("suspendFlag") == 0).to_pandas()

    invalid_rows = len(df) - len(valid_data)

    if invalid_rows > 0:
        print(f"删除了 {invalid_rows} 行 suspendFlag 非0的数据")
        print(f"原始数据行数: {len(df)}, 处理后数据行数: {len(valid_data)}")
    else:
        print("没有发现 suspendFlag 非0的数据")

    return valid_data


def plot_heikin_ashi(df, savefig=None, title=None, show_zlsma=True, zlsma_period=32):
    """
    绘制 Heikin Ashi 蜡烛图
    
    参数:
    df: DataFrame, 包含 Time, open, high, low, close 列的数据框
    savefig: 图片保存路径，默认为None（不保存）
    title: 图表标题，默认None
    show_zlsma: 布尔值, 是否显示ZLSMA线, 默认True
    zlsma_period: 整数, ZLSMA计算周期, 默认32
    """
    # 确保时间列格式正确
    df = df.copy()
    df['Time'] = pd.to_datetime(df['Time'])
    
    print(f"\n[DEBUG] 绘图前数据样本 - 时间范围: {df['Time'].min()} 到 {df['Time'].max()}")
    print(f"[DEBUG] 共有 {len(df)} 条5分钟K线数据")

    # 计算 Heikin Ashi 数据
    ha_df = calculate_heikin_ashi(df)

    # 设置索引为时间列，用于 mplfinance
    ha_df.set_index('Time', inplace=True)
    
    print(f"\n[DEBUG] 转换为Heikin Ashi后的数据样本:")
    print(ha_df[['ha_open', 'ha_high', 'ha_low', 'ha_close']].head())

    # 创建 mplfinance 可用的 OHLC 数据
    ohlc_df = pd.DataFrame({
        'Open': ha_df['ha_open'],
        'High': ha_df['ha_high'],
        'Low': ha_df['ha_low'],
        'Close': ha_df['ha_close']
    }, index=ha_df.index)
    
    print(f"\n[DEBUG] 准备绘图的OHLC数据样本:")
    print(ohlc_df.head())

    # 创建自定义样式
    mc = mpf.make_marketcolors(
        up='red',  # 上涨为红色
        down='green',  # 下跌为绿色
        edge='inherit',
        wick='inherit',
        volume='inherit'
    )

    # 创建样式
    s = mpf.make_mpf_style(
        marketcolors=mc,
        gridstyle='--',
        y_on_right=False
    )

    # 准备附加图表
    apds = []

    # 计算并添加ZLSMA
    zlsma_series = None
    if show_zlsma:
        # 计算ZLSMA
        zlsma_values = ZLSMA(ha_df['ha_close'].values, zlsma_period)

        # 创建ZLSMA Series
        zlsma_series = pd.Series(zlsma_values, index=ohlc_df.index)
        
        print(f"\n[DEBUG] ZLSMA计算结果样本:")
        print(zlsma_series.head())
        
        # 检查NaN值
        nan_count = zlsma_series.isna().sum()
        print(f"[DEBUG] ZLSMA中有 {nan_count} 个NaN值")
        print(f"[DEBUG] 第一个非NaN值的索引: {zlsma_series.first_valid_index() if not zlsma_series.isna().all() else 'None'}")

        # 添加ZLSMA到图表
        apds.append(mpf.make_addplot(zlsma_series, color='yellow', width=1.5, panel=0))

    # 设置图表标题
    if title is None:
        title = 'Heikin Ashi 5-Minute Candlestick Chart'
    if show_zlsma:
        title += f' with ZLSMA({zlsma_period})'
    
    print(f"\n[DEBUG] 开始绘制图表: {title}")

    # 绘制 Heikin Ashi 蜡烛图
    fig, axes = mpf.plot(
        ohlc_df,
        type='candle',
        style=s,
        title=title,
        ylabel='Price',
        volume=False,
        addplot=apds,
        returnfig=True,
        figsize=(15, 7),
        tight_layout=True,
        warn_too_much_data=100000
    )

    try:
        # 创建评分器实例
        print("开始初始化评分器...")
        scorer = HeikinAshiScorer(df)
        print("评分器初始化完成")
        
        # 添加"涨"和"跌"标记以及打分
        if show_zlsma and zlsma_series is not None:
            # 获取主图的坐标轴
            ax = axes[0]
            
            # 获取图表的X轴日期位置映射
            print("获取X轴位置映射...")
            
            # 获取X轴上的日期位置
            x_axis_dates = ohlc_df.index
            
            # 创建日期到X轴位置的映射
            date_to_x_position = {}
            for i, date in enumerate(x_axis_dates):
                date_to_x_position[date] = i
            
            print(f"X轴位置映射样本: {list(date_to_x_position.items())[:3]}...")
            
            # 将数据转换为列表，避免Series问题
            print("准备原始K线数据...")
            
            # 确保我们有正确的日期列表和价格列表
            orig_dates = df['Time'].tolist()
            orig_highs = df['high'].tolist()
            orig_lows = df['low'].tolist()
            
            print("\n[DEBUG] 开始标记满足条件的K线并打分")
            print(f"[DEBUG] 注意：将跳过ZLSMA的前 {zlsma_period*2} 个值，因为它们可能是NaN")
            
            # 从ZLSMA有效值开始处理
            valid_start_idx = 0
            if not zlsma_series.isna().all():
                first_valid_date = zlsma_series.first_valid_index()
                if first_valid_date in date_to_x_position:
                    valid_start_idx = date_to_x_position[first_valid_date]
                    print(f"[DEBUG] 从索引 {valid_start_idx} 开始处理 (日期: {first_valid_date})")
            
            # 获取Y轴的范围，用于计算标记的相对位置
            y_min, y_max = ax.get_ylim()
            y_range = y_max - y_min
            y_offset = y_range * 0.01  # 使用1%的Y轴范围作为基本偏移量
            
            # 存储所有潜在的买卖点
            potential_buy_points = []
            potential_sell_points = []
            
            # 遍历每个K线
            for date, x_position in date_to_x_position.items():
                try:
                    # 跳过ZLSMA无效的部分
                    if x_position < valid_start_idx:
                        continue
                    
                    # 获取当前K线数据
                    if date not in ohlc_df.index:
                        continue
                        
                    open_price = float(ohlc_df.loc[date, 'Open'])
                    close_price = float(ohlc_df.loc[date, 'Close'])
                    high_price = float(ohlc_df.loc[date, 'High'])
                    low_price = float(ohlc_df.loc[date, 'Low'])
                    
                    # 获取对应的ZLSMA值
                    if pd.isna(zlsma_series.loc[date]):
                        continue
                        
                    zlsma_value = float(zlsma_series.loc[date])
                    
                    # 获取对应时间点的普通K线数据
                    date_idx = None
                    for j, d in enumerate(orig_dates):
                        if d == date:
                            date_idx = j
                            break
                    
                    if date_idx is None:
                        continue
                        
                    orig_high = float(orig_highs[date_idx])
                    orig_low = float(orig_lows[date_idx])
                    
                    # 获取前一根K线的信息（如果存在）
                    prev_ha_high = None
                    prev_ha_low = None
                    prev_ha_is_red = False
                    prev_ha_is_green = False
                    
                    prev_idx = x_position - 1
                    if prev_idx >= 0 and prev_idx < len(x_axis_dates):
                        prev_date = x_axis_dates[prev_idx]
                        if prev_date in ohlc_df.index:
                            prev_open = float(ohlc_df.loc[prev_date, 'Open'])
                            prev_close = float(ohlc_df.loc[prev_date, 'Close'])
                            prev_ha_high = float(ohlc_df.loc[prev_date, 'High'])
                            prev_ha_low = float(ohlc_df.loc[prev_date, 'Low'])
                            prev_ha_is_red = prev_close > prev_open
                            prev_ha_is_green = prev_close < prev_open
                    
                    # 如果是红色K线(上涨)且最高价高于ZLSMA
                    if close_price > open_price and high_price > zlsma_value:
                        # 使用评分器计算分数
                        score = scorer.score_red_candle(
                            orig_high, orig_highs, date_idx, 
                            high_price, prev_ha_high, prev_ha_is_red, date
                        )
                        
                        # 使用相对于Y轴范围的偏移量，确保标记位置正确
                        # 在K线下方标记"涨"
                        ax.text(x_position, low_price - y_offset, '涨', color='red', 
                                fontweight='bold', ha='center', va='top', fontsize=10)
                        
                        # 在"涨"标记下方添加普通K线的最高价
                        ax.text(x_position, low_price - y_offset*3, f'{orig_high:.2f}', color='red',
                                fontweight='bold', ha='center', va='top', fontsize=8)
                        
                        # 在价格下方添加分数
                        ax.text(x_position, low_price - y_offset*5, f'得分:{score}', color='red',
                                fontweight='bold', ha='center', va='top', fontsize=8)
                        
                        print(f"[DEBUG] 标记红色K线 - 时间: {date}, X位置: {x_position}, 分数: {score}, 原始最高价: {orig_high:.2f}")
                        
                        # 如果分数大于等于4，添加到潜在买点列表
                        if score >= 4:
                            potential_buy_points.append({
                                'date': date,
                                'x_position': x_position,
                                'price': high_price,
                                'score': score
                            })
                    
                    # 如果是绿色K线(下跌)且最低价低于ZLSMA
                    elif close_price < open_price and low_price < zlsma_value:
                        # 使用评分器计算分数
                        score = scorer.score_green_candle(
                            orig_low, orig_lows, date_idx, 
                            low_price, prev_ha_low, prev_ha_is_green, date
                        )
                        
                        # 使用相对于Y轴范围的偏移量，确保标记位置正确
                        # 在K线下方标记"跌"
                        ax.text(x_position, low_price - y_offset, '跌', color='green', 
                                fontweight='bold', ha='center', va='top', fontsize=10)
                        
                        # 在"跌"标记下方添加普通K线的最低价
                        ax.text(x_position, low_price - y_offset*3, f'{orig_low:.2f}', color='green',
                                fontweight='bold', ha='center', va='top', fontsize=8)
                        
                        # 在价格下方添加分数
                        ax.text(x_position, low_price - y_offset*5, f'得分:{score}', color='green',
                                fontweight='bold', ha='center', va='top', fontsize=8)
                        
                        print(f"[DEBUG] 标记绿色K线 - 时间: {date}, X位置: {x_position}, 分数: {score}, 原始最低价: {orig_low:.2f}")
                        
                        # 如果分数大于等于4，添加到潜在卖点列表
                        if score >= 4:
                            potential_sell_points.append({
                                'date': date,
                                'x_position': x_position,
                                'price': low_price,
                                'score': score
                            })
                except Exception as e:
                    print(f"处理K线 {date} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue
            
            # 按照买卖买卖买卖的固定顺序选取买卖点
            print("\n[DEBUG] 开始按照买卖买卖买卖的固定顺序选取买卖点")
            print(f"[DEBUG] 潜在买点数量: {len(potential_buy_points)}")
            print(f"[DEBUG] 潜在卖点数量: {len(potential_sell_points)}")
            
            # 按时间排序
            potential_buy_points.sort(key=lambda x: x['date'])
            potential_sell_points.sort(key=lambda x: x['date'])
            
            # 选取买卖点
            trade_points = []
            current_state = 'buy'  # 从买点开始
            
            # 找到第一个可用的买点和卖点
            buy_idx = 0
            sell_idx = 0
            
            # 改进的买卖点选择逻辑
            while buy_idx < len(potential_buy_points) or sell_idx < len(potential_sell_points):
                if current_state == 'buy':
                    # 如果没有更多的买点，结束循环
                    if buy_idx >= len(potential_buy_points):
                        break
                        
                    # 获取当前买点
                    buy_point = potential_buy_points[buy_idx]
                    
                    # 如果已经有卖点，确保这个买点在最后一个卖点之后
                    if trade_points and trade_points[-1]['type'] == 'sell':
                        if buy_point['date'] <= trade_points[-1]['date']:
                            buy_idx += 1
                            continue
                    
                    # 添加买点
                    trade_points.append({'type': 'buy', **buy_point})
                    print(f"[DEBUG] 添加买点 - 时间: {buy_point['date']}, 分数: {buy_point['score']}")
                    
                    # 更新状态和索引
                    current_state = 'sell'
                    buy_idx += 1
                else:  # current_state == 'sell'
                    # 如果没有更多的卖点，结束循环
                    if sell_idx >= len(potential_sell_points):
                        break
                        
                    # 获取当前卖点
                    sell_point = potential_sell_points[sell_idx]
                    
                    # 确保这个卖点在最后一个买点之后
                    if trade_points and trade_points[-1]['type'] == 'buy':
                        if sell_point['date'] <= trade_points[-1]['date']:
                            sell_idx += 1
                            continue
                    
                    # 添加卖点
                    trade_points.append({'type': 'sell', **sell_point})
                    print(f"[DEBUG] 添加卖点 - 时间: {sell_point['date']}, 分数: {sell_point['score']}")
                    
                    # 更新状态和索引
                    current_state = 'buy'
                    sell_idx += 1
            
            # 标记买卖点
            print(f"\n[DEBUG] 最终选取的买卖点数量: {len(trade_points)}")
            print("\n[DEBUG] 买卖点对应的普通K线收盘价:")
            for i, point in enumerate(trade_points):
                x_date = point['date']
                x_position = point['x_position']
                price = point['price']
                
                # 获取当前K线的低点，确保标记在正下方
                if x_date not in ohlc_df.index:
                    continue
                
                current_low = float(ohlc_df.loc[x_date, 'Low'])
                
                # 获取对应时间点的普通K线收盘价
                orig_close = None
                for j, d in enumerate(orig_dates):
                    if d == x_date:
                        orig_close = df.iloc[j]['close']
                        break
                
                if point['type'] == 'buy':
                    # 在K线正下方标记买点
                    ax.text(x_position, current_low - y_offset*7, 'BUY', 
                            color='red',
                            fontweight='bold', 
                            ha='center', 
                            va='top', 
                            fontsize=12,
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='red', boxstyle='round,pad=0.3'))
                    print(f"[DEBUG] 买点 - 时间: {x_date}, Heikin Ashi价格: {price:.2f}, 普通K线收盘价: {orig_close:.2f}, 分数: {point['score']}")
                else:  # point['type'] == 'sell'
                    # 在K线正下方标记卖点
                    ax.text(x_position, current_low - y_offset*7, 'SELL', 
                            color='green',
                            fontweight='bold', 
                            ha='center', 
                            va='top', 
                            fontsize=12,
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='green', boxstyle='round,pad=0.3'))
                    print(f"[DEBUG] 卖点 - 时间: {x_date}, Heikin Ashi价格: {price:.2f}, 普通K线收盘价: {orig_close:.2f}, 分数: {point['score']}")
    except Exception as e:
        print(f"打分过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

    # 保存图片或显示
    if savefig:
        plt.savefig(savefig)
        print(f"图片已保存到: {savefig}")
    else:
        print("[DEBUG] 显示图表")
        plt.show()

    plt.close()


def process_stock_file(file_path, output_path):
    """
    处理单个股票文件，计算 Heikin Ashi 数据并绘制图表
    
    参数:
    file_path: str, 股票数据文件路径
    output_path: str, 输出日志文件路径
    """
    try:
        # 尝试不同的编码方式读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件: {file_path}")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取时出现其他错误: {str(e)}")
                continue
        
        if df is None:
            raise ValueError(f"无法使用任何已知编码读取文件: {file_path}")

        # 确保必要的列存在
        required_cols = ['Time', 'open', 'high', 'low', 'close']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"文件 {file_path} 缺少必要的列: {col}")

        # 处理时间列
        df['Time'] = pd.to_datetime(df['Time'])
        
        print(f"\n[DEBUG] 原始数据信息:")
        print(f"文件: {file_path}")
        print(f"数据行数: {len(df)}")
        print(f"时间范围: {df['Time'].min()} 到 {df['Time'].max()}")
        print(f"数据样本:")
        print(df[['Time', 'open', 'high', 'low', 'close']].head())

        # 获取股票代码
        stock_code = os.path.splitext(os.path.basename(file_path))[0]

        # 直接绘制 Heikin Ashi 图表，包含ZLSMA，并弹窗显示
        plot_heikin_ashi(
            df,
            show_zlsma=True,
            zlsma_period=32,
            title=f"{stock_code} 5-Min Heikin Ashi Chart with ZLSMA"
        )

        print(f"处理完成: {file_path}")

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"处理文件 {file_path} 时出错: {str(e)}\n")
            f.write(f"处理时间: {datetime.now()}\n")


# 使用示例
if __name__ == "__main__":
    start_time = datetime.now()
    # 获取所有CSV文件路径
    csv_folder = "stock_data"
    csv_files = [os.path.join(csv_folder, f) for f in os.listdir(csv_folder) if f.endswith('.csv')]

    # 创建日志文件夹
    log_folder = "logs"
    os.makedirs(log_folder, exist_ok=True)

    # 创建输出文件夹
    output_folder = "heikin_ashi_output"
    os.makedirs(output_folder, exist_ok=True)

    # 使用多进程处理所有CSV文件
    with multiprocessing.Pool() as pool:
        results = []
        for file_path in csv_files:
            # 为每个股票创建一个日志文件
            stock_code = os.path.splitext(os.path.basename(file_path))[0]
            log_file = os.path.join(log_folder, f"{stock_code}_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")

            result = pool.apply_async(process_stock_file, args=(file_path, log_file))
            results.append(result)

        # 等待所有进程完成
        for result in results:
            result.get()

    print("所有股票处理完成!")

    end_time = datetime.now()
    print(f"程序运行时间: {end_time - start_time}")
